# ORA Hume EVI Integration - Complete Setup Guide

## 📋 Table of Contents
1. [Overview & Architecture](#overview--architecture)
2. [Prerequisites & Installation](#prerequisites--installation)
3. [Database Setup](#database-setup)
4. [Authentication Setup](#authentication-setup)
5. [Hume AI Setup](#hume-ai-setup)
6. [Application Configuration](#application-configuration)
7. [Running the Application](#running-the-application)
8. [Testing & Verification](#testing--verification)
9. [Troubleshooting](#troubleshooting)
10. [Production Deployment](#production-deployment)

---

## 🏗️ Overview & Architecture

### What This Application Does
ORA is a complete voice-enabled chat application that uses Hume AI's Empathic Voice Interface (EVI) to provide emotionally intelligent conversations. Users can log in with Google, have voice conversations with an AI that understands emotions, and view detailed analytics about their interactions.

### Architecture Overview

```
┌─────────────────────────────────────────────────────────────┐
│                    USER INTERFACE                           │
│  React Frontend (Port 5173) - Tailwind CSS + TypeScript   │
│  • Login/Logout • Voice Chat • Analytics • Profile        │
└─────────────────┬───────────────────────────────────────────┘
                  │ HTTP/WebSocket
┌─────────────────▼───────────────────────────────────────────┐
│                 EXPRESS BACKEND                             │
│  Node.js/Express API (Port 3001) - TypeScript             │
│  • Google OAuth • JWT Auth • WebSocket • REST APIs        │
└─────────────────┬───────────────────────────────────────────┘
                  │
        ┌─────────▼─────────┐         ┌─────────────────┐
        │   POSTGRESQL      │         │   HUME AI API   │
        │   Database        │         │   Voice + AI    │
        │   (Port 5432)     │         │   Emotions      │
        └───────────────────┘         └─────────────────┘
```

### Key Components

**Frontend (React + TypeScript)**
- **Authentication**: Google OAuth login/logout
- **Voice Interface**: Real-time voice chat with Hume AI
- **Analytics Dashboard**: Emotion analysis and conversation history
- **User Profile**: Personal settings and preferences
- **Real-time Updates**: WebSocket connection for live chat

**Backend (Express + TypeScript)**
- **Authentication Service**: Google OAuth integration with JWT tokens
- **Chat Management**: Session creation, message handling, transcripts
- **Hume Integration**: Proxy to Hume AI API for voice processing
- **WebSocket Server**: Real-time bidirectional communication
- **Database Layer**: PostgreSQL integration with proper models
- **Security**: Rate limiting, CORS, input validation, helmet.js

**Database (PostgreSQL)**
- **Users Table**: Google OAuth user data and profiles
- **Chat Sessions**: Conversation metadata and status
- **Messages**: All conversation content with timestamps
- **Emotions**: Detailed emotion analysis data
- **Indexes**: Optimized for fast queries and analytics

**External Services**
- **Google OAuth**: Secure user authentication
- **Hume AI EVI**: Voice processing and emotion analysis

---

## 🔧 Prerequisites & Installation

### System Requirements
- **Operating System**: macOS, Linux, or Windows with WSL2
- **Node.js**: Version 18 or higher
- **PostgreSQL**: Version 12 or higher
- **Git**: For cloning the repository
- **Web Browser**: Chrome, Firefox, Safari, or Edge

### Installing Prerequisites

#### 1. Install Node.js
**macOS (using Homebrew):**
```bash
# Install Homebrew if you don't have it
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# Install Node.js
brew install node

# Verify installation
node --version  # Should show v18.x.x or higher
npm --version   # Should show 9.x.x or higher
```

**Windows:**
1. Download Node.js from [nodejs.org](https://nodejs.org/)
2. Run the installer and follow the setup wizard
3. Open Command Prompt and verify: `node --version`

**Linux (Ubuntu/Debian):**
```bash
# Update package index
sudo apt update

# Install Node.js
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Verify installation
node --version
npm --version
```

#### 2. Install PostgreSQL
**macOS (using Homebrew):**
```bash
# Install PostgreSQL
brew install postgresql

# Start PostgreSQL service
brew services start postgresql

# Verify installation
psql --version
```

**Windows:**
1. Download PostgreSQL from [postgresql.org](https://www.postgresql.org/download/windows/)
2. Run the installer
3. Remember the password you set for the `postgres` user
4. Add PostgreSQL to your PATH if not done automatically

**Linux (Ubuntu/Debian):**
```bash
# Install PostgreSQL
sudo apt update
sudo apt install postgresql postgresql-contrib

# Start PostgreSQL service
sudo systemctl start postgresql
sudo systemctl enable postgresql

# Switch to postgres user and set password
sudo -u postgres psql
\password postgres
\q
```

#### 3. Install Git (if not already installed)
**macOS:**
```bash
brew install git
```

**Windows:**
Download from [git-scm.com](https://git-scm.com/download/win)

**Linux:**
```bash
sudo apt install git
```

---

## 🗄️ Database Setup

### Understanding PostgreSQL Basics
PostgreSQL is a powerful, open-source relational database. Think of it as a structured way to store and retrieve data, like a very sophisticated spreadsheet that multiple applications can access simultaneously.

### Step 1: Verify PostgreSQL Installation
```bash
# Check if PostgreSQL is running
pg_isready

# If it says "accepting connections" - you're good!
# If not, start the service:

# macOS:
brew services start postgresql

# Linux:
sudo systemctl start postgresql

# Windows:
# Use the Services app to start "postgresql-x64-xx"
```

### Step 2: Access PostgreSQL
```bash
# Connect to PostgreSQL as the default user
psql -U postgres

# You should see a prompt like: postgres=#
# This means you're connected to the database
```

### Step 3: Create the Application Database
While connected to PostgreSQL, run:
```sql
-- Create the database for our application
CREATE DATABASE ora_hume_db;

-- Exit PostgreSQL
\q
```

### Step 4: Set Up Database Schema
Navigate to your project directory and run:
```bash
# This creates all the tables and relationships our app needs
psql -U postgres -d ora_hume_db -f setup-database.sql
```

**What this creates:**
- **users table**: Stores Google OAuth user information
- **chat_sessions table**: Tracks each conversation session
- **conversation_messages table**: Stores all chat messages and emotions
- **Indexes**: Makes database queries fast
- **Triggers**: Automatically updates timestamps

### Step 5: Verify Database Setup
```bash
# Connect to your new database
psql -U postgres -d ora_hume_db

# List all tables
\dt

# You should see: users, chat_sessions, conversation_messages
# Exit
\q
```

---

## 🔐 Authentication Setup (Google OAuth)

### Understanding OAuth
OAuth is like giving someone a temporary key to your house instead of your actual house key. Google OAuth lets users log into your app using their Google account without sharing their Google password with you.

### Step 1: Create Google Cloud Project
1. **Go to Google Cloud Console**
   - Visit [console.cloud.google.com](https://console.cloud.google.com/)
   - Sign in with your Google account

2. **Create a New Project**
   - Click "Select a project" at the top
   - Click "New Project"
   - Name it "ORA Hume App" (or any name you prefer)
   - Click "Create"

3. **Select Your Project**
   - Make sure your new project is selected in the dropdown

### Step 2: Enable Required APIs
1. **Navigate to APIs & Services**
   - In the left sidebar, click "APIs & Services" → "Library"

2. **Enable Google+ API**
   - Search for "Google+ API"
   - Click on it and press "Enable"
   - Wait for it to be enabled

### Step 3: Create OAuth Credentials
1. **Go to Credentials**
   - In the left sidebar, click "APIs & Services" → "Credentials"

2. **Create OAuth Consent Screen**
   - Click "OAuth consent screen" tab
   - Choose "External" (unless you have a Google Workspace)
   - Fill in required fields:
     - App name: "ORA Hume App"
     - User support email: Your email
     - Developer contact: Your email
   - Click "Save and Continue"
   - Skip "Scopes" for now (click "Save and Continue")
   - Skip "Test users" for now (click "Save and Continue")

3. **Create OAuth Client ID**
   - Click "Credentials" tab
   - Click "Create Credentials" → "OAuth client ID"
   - Choose "Web application"
   - Name it "ORA Web Client"
   - Add Authorized JavaScript origins:
     - `http://localhost:5173` (for frontend)
     - `http://localhost:3001` (for backend)
   - Add Authorized redirect URIs:
     - `http://localhost:3001/auth/google/callback`
   - Click "Create"

4. **Save Your Credentials**
   - Copy the "Client ID" (looks like: `*********-abcdef.apps.googleusercontent.com`)
   - Copy the "Client Secret" (looks like: `GOCSPX-abcdef123456`)
   - **Keep these safe!** You'll need them in the next step

---

## 🤖 Hume AI Setup

### Understanding Hume AI
Hume AI provides emotional intelligence for voice interactions. It can detect emotions in speech, generate empathetic responses, and create natural voice conversations.

### Step 1: Create Hume AI Account
1. **Sign Up**
   - Visit [hume.ai](https://hume.ai/)
   - Click "Get Started" or "Sign Up"
   - Create your account

2. **Verify Email**
   - Check your email and verify your account

### Step 2: Get API Credentials
1. **Access Dashboard**
   - Log into your Hume AI account
   - Navigate to the dashboard

2. **Get API Key**
   - Look for "API Keys" or "Credentials" section
   - Copy your API Key (looks like: `hume_api_key_123abc456def`)

3. **Get Secret Key** (if available)
   - Copy your Secret Key if provided
   - Some accounts may not have this initially

### Step 3: Create EVI Configuration
1. **Navigate to EVI Section**
   - Look for "Empathic Voice Interface" or "EVI" in the dashboard

2. **Create Configuration**
   - Click "Create Configuration" or similar
   - Give it a name like "ORA Chat Config"
   - Configure voice settings (you can use defaults for now)
   - Save the configuration

3. **Get Configuration ID**
   - Copy the Configuration ID (looks like: `config_123abc456def`)

**Note**: If you can't access EVI features immediately, you may need to:
- Contact Hume AI support for access
- Wait for account approval
- Use placeholder values for initial setup

---

## ⚙️ Application Configuration

### Step 1: Clone and Navigate to Project
```bash
# Clone the repository (if you haven't already)
git clone <your-repo-url>
cd ORA-P1

# Verify you're in the right directory
ls -la
# You should see: backend/, frontend/, setup-database.sql, etc.
```

### Step 2: Configure Backend Environment
```bash
# Navigate to backend directory
cd backend

# Create environment file from template
cp .env.example .env

# Edit the environment file
nano .env  # or use your preferred editor
```

**Edit `backend/.env` with your actual values:**
```env
# Database Configuration
DATABASE_URL=postgresql://postgres:password@localhost:5432/ora_hume_db

# JWT Configuration (generate strong secrets for production)
JWT_SECRET=your-super-secret-jwt-key-change-in-production
JWT_EXPIRES_IN=7d

# Google OAuth Configuration (from Google Cloud Console)
GOOGLE_CLIENT_ID=*********-abcdef.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=GOCSPX-abcdef123456

# Hume AI Configuration (from Hume AI Dashboard)
HUME_API_KEY=hume_api_key_123abc456def
HUME_SECRET_KEY=hume_secret_key_789ghi012jkl
HUME_CONFIG_ID=config_123abc456def
HUME_BASE_URL=https://api.hume.ai

# Server Configuration
PORT=3001
NODE_ENV=development
BACKEND_URL=http://localhost:3001

# Session Configuration (generate strong secret for production)
SESSION_SECRET=your-session-secret-change-in-production
```

**Important Notes:**
- Replace `password` in DATABASE_URL with your actual PostgreSQL password
- Replace all placeholder values with your actual credentials
- For production, generate strong, unique secrets for JWT_SECRET and SESSION_SECRET

### Step 3: Configure Frontend Environment
```bash
# Navigate to frontend directory
cd ../frontend

# Create environment file
cp .env.example .env

# Edit the environment file
nano .env  # or use your preferred editor
```

**Edit `frontend/.env` with your values:**
```env
# API Configuration
VITE_API_URL=http://localhost:3001
VITE_WS_URL=ws://localhost:3001

# Google OAuth Configuration (same Client ID as backend)
VITE_GOOGLE_CLIENT_ID=*********-abcdef.apps.googleusercontent.com

# Hume AI Configuration (same API key as backend)
VITE_HUME_API_KEY=hume_api_key_123abc456def
```

### Step 4: Install Dependencies
```bash
# Install backend dependencies
cd ../backend
npm install

# Install frontend dependencies
cd ../frontend
npm install

# Return to project root
cd ..
```

---

## 🚀 Running the Application

### Method 1: Automatic Startup (Recommended)
```bash
# Make the startup script executable
chmod +x start-dev.sh

# Run the startup script
./start-dev.sh
```

This script will:
- Check if PostgreSQL is running
- Create the database if it doesn't exist
- Start both backend and frontend servers
- Show you the URLs to access the application

### Method 2: Manual Startup

#### Terminal 1 - Backend
```bash
cd backend
npm run dev
```
You should see:
```
🚀 Server running on port 3001
✅ Database connected successfully
🔗 WebSocket server ready
```

#### Terminal 2 - Frontend
```bash
cd frontend
npm run dev
```
You should see:
```
  VITE v5.x.x  ready in xxx ms

  ➜  Local:   http://localhost:5173/
  ➜  Network: use --host to expose
```

### Method 3: Docker (Alternative)
```bash
# Create environment file for Docker
cp .env.example .env
# Edit .env with your credentials

# Start all services
docker-compose up

# To run in background
docker-compose up -d
```

---

## ✅ Testing & Verification

### Step 1: Access the Application
1. **Open your web browser**
2. **Navigate to**: `http://localhost:5173`
3. **You should see**: The ORA login page

### Step 2: Test Authentication
1. **Click "Login with Google"**
2. **You should be redirected** to Google's login page
3. **Sign in** with your Google account
4. **Grant permissions** when prompted
5. **You should be redirected back** to the ORA dashboard

### Step 3: Test Backend API
Open a new browser tab and visit:
- `http://localhost:3001/health` - Should show "OK"
- `http://localhost:3001/api/auth/me` - Should show user info (if logged in)

### Step 4: Test Database Connection
```bash
# Connect to database
psql -U postgres -d ora_hume_db

# Check if user was created after login
SELECT * FROM users;

# You should see your Google account information
\q
```

### Step 5: Test Voice Chat (if Hume credentials are configured)
1. **Navigate to Chat page** in the application
2. **Click "Start Chat"**
3. **Allow microphone access** when prompted
4. **Speak into your microphone**
5. **You should see**: Transcription and emotion analysis

---

## 🔧 Troubleshooting

### Common Issues and Solutions

#### 1. "PostgreSQL connection failed"
**Problem**: Can't connect to database
**Solutions**:
```bash
# Check if PostgreSQL is running
pg_isready

# Start PostgreSQL
# macOS:
brew services start postgresql

# Linux:
sudo systemctl start postgresql

# Check if database exists
psql -U postgres -l | grep ora_hume_db
```

#### 2. "Google OAuth error"
**Problem**: Authentication fails
**Solutions**:
- Verify your Google Client ID and Secret in `.env` files
- Check that redirect URIs are correctly configured in Google Cloud Console
- Ensure both frontend and backend URLs are in authorized origins

#### 3. "Hume API error"
**Problem**: Voice chat doesn't work
**Solutions**:
- Verify your Hume API credentials
- Check if you have access to EVI features
- Contact Hume AI support if needed
- Use placeholder credentials for initial testing

#### 4. "Port already in use"
**Problem**: Can't start servers
**Solutions**:
```bash
# Find what's using the port
lsof -i :3001  # for backend
lsof -i :5173  # for frontend

# Kill the process
kill -9 <PID>

# Or use different ports in your .env files
```

#### 5. "Module not found" errors
**Problem**: Dependencies missing
**Solutions**:
```bash
# Clear node_modules and reinstall
rm -rf node_modules package-lock.json
npm install

# Do this for both backend and frontend directories
```

#### 6. "Database schema errors"
**Problem**: Tables don't exist
**Solutions**:
```bash
# Recreate database
psql -U postgres
DROP DATABASE ora_hume_db;
CREATE DATABASE ora_hume_db;
\q

# Run setup script again
psql -U postgres -d ora_hume_db -f setup-database.sql
```

### Getting Help
1. **Check the logs**: Look at terminal output for specific error messages
2. **Verify environment variables**: Ensure all credentials are correct
3. **Test components individually**: Database, backend API, frontend separately
4. **Check network connectivity**: Ensure you can reach external APIs

---

## 🌐 Production Deployment

### Security Considerations
Before deploying to production:

1. **Change all default secrets**:
   ```env
   JWT_SECRET=<generate-strong-random-string>
   SESSION_SECRET=<generate-strong-random-string>
   ```

2. **Use environment variables** for all sensitive data
3. **Enable HTTPS** for all communications
4. **Set up proper CORS** policies
5. **Configure rate limiting**
6. **Set up monitoring and logging**

### Environment Setup
1. **Update environment variables** for production URLs
2. **Configure production database** (not localhost)
3. **Set up SSL certificates**
4. **Configure reverse proxy** (nginx/Apache)

### Deployment Options
- **Cloud Platforms**: AWS, Google Cloud, Azure
- **Container Platforms**: Docker, Kubernetes
- **Platform-as-a-Service**: Heroku, Vercel, Railway

### Database Considerations
- **Use managed PostgreSQL** service in production
- **Set up regular backups**
- **Configure connection pooling**
- **Monitor performance**

---

## 📚 Additional Resources

### Documentation
- [Hume AI Documentation](https://docs.hume.ai/)
- [Google OAuth Documentation](https://developers.google.com/identity/protocols/oauth2)
- [PostgreSQL Documentation](https://www.postgresql.org/docs/)
- [Express.js Documentation](https://expressjs.com/)
- [React Documentation](https://react.dev/)

### Support
- **Hume AI Support**: Contact through their dashboard
- **Google Cloud Support**: Available in Google Cloud Console
- **Community Forums**: Stack Overflow, Reddit, Discord

This guide should get you up and running with the ORA Hume EVI integration. The application provides a solid foundation for voice-enabled, emotionally intelligent chat applications with proper authentication, data persistence, and analytics capabilities.
