errors:
  UnprocessableEntityError:
    status-code: 422
    type: HTTPValidationError
    docs: Validation Error
    examples:
      - value: {}
  BadRequestError:
    status-code: 400
    type: ErrorResponse
    docs: Bad Request
    examples:
      - value: {}
service:
  auth: false
  base-path: ''
  endpoints:
    custom_language_model_supports_tool_use_v0_evi_custom_language_model_supports_tool_use_post:
      path: /v0/evi/custom_language_model_supports_tool_use
      method: POST
      auth: true
      source:
        openapi: evi-openapi.json
      display-name: Custom Language Model Supports Tool Use
      request:
        name: >-
          BodyCustomLanguageModelSupportsToolUseV0EviCustomLanguageModelSupportsToolUsePost
        body:
          properties:
            model_resource: string
        content-type: application/json
      response:
        docs: Successful Response
        type: SupportsToolUse
        status-code: 200
      errors:
        - UnprocessableEntityError
      examples:
        - request:
            model_resource: model_resource
          response:
            body:
              model_resource: model_resource
              supports_tool_use: true
  source:
    openapi: evi-openapi.json
types:
  AssistantEnd:
    docs: When provided, the output is an assistant end message.
    properties:
      custom_session_id:
        type: optional<string>
        docs: >-
          Used to manage conversational state, correlate frontend and backend
          data, and persist conversations across EVI sessions.
      type:
        type: literal<"assistant_end">
        docs: >-
          The type of message sent through the socket; for an Assistant End
          message, this must be `assistant_end`.


          This message indicates the conclusion of the assistant’s response,
          signaling that the assistant has finished speaking for the current
          conversational turn.
    source:
      openapi: evi-asyncapi.json
  AssistantInput:
    docs: When provided, the input is spoken by EVI.
    properties:
      custom_session_id:
        type: optional<string>
        docs: >-
          Used to manage conversational state, correlate frontend and backend
          data, and persist conversations across EVI sessions.
      text:
        type: string
        docs: >-
          Assistant text to synthesize into spoken audio and insert into the
          conversation.


          EVI uses this text to generate spoken audio using our proprietary
          expressive text-to-speech model. Our model adds appropriate emotional
          inflections and tones to the text based on the user’s expressions and
          the context of the conversation. The synthesized audio is streamed
          back to the user as an [Assistant
          Message](/reference/empathic-voice-interface-evi/chat/chat#receive.AssistantMessage.type).
      type:
        type: literal<"assistant_input">
        docs: >-
          The type of message sent through the socket; must be `assistant_input`
          for our server to correctly identify and process it as an Assistant
          Input message.
    source:
      openapi: evi-asyncapi.json
  AssistantMessage:
    docs: When provided, the output is an assistant message.
    properties:
      custom_session_id:
        type: optional<string>
        docs: >-
          Used to manage conversational state, correlate frontend and backend
          data, and persist conversations across EVI sessions.
      from_text:
        type: boolean
        docs: >-
          Indicates if this message was inserted into the conversation as text
          from an [Assistant Input
          message](/reference/empathic-voice-interface-evi/chat/chat#send.AssistantInput.text).
      id:
        type: optional<string>
        docs: >-
          ID of the assistant message. Allows the Assistant Message to be
          tracked and referenced.
      message:
        type: ChatMessage
        docs: Transcript of the message.
      models:
        type: Inference
        docs: Inference model results.
      type:
        type: literal<"assistant_message">
        docs: >-
          The type of message sent through the socket; for an Assistant Message,
          this must be `assistant_message`.


          This message contains both a transcript of the assistant’s response
          and the expression measurement predictions of the assistant’s audio
          output.
    source:
      openapi: evi-asyncapi.json
  AssistantProsody:
    docs: When provided, the output is an Assistant Prosody message.
    properties:
      custom_session_id:
        type: optional<string>
        docs: >-
          Used to manage conversational state, correlate frontend and backend
          data, and persist conversations across EVI sessions.
      id:
        type: optional<string>
        docs: Unique identifier for the segment.
      models:
        type: Inference
        docs: Inference model results.
      type:
        type: literal<"assistant_prosody">
        docs: >-
          The type of message sent through the socket; for an Assistant Prosody
          message, this must be `assistant_PROSODY`.


          This message the expression measurement predictions of the assistant's
          audio output.
    source:
      openapi: evi-asyncapi.json
  AudioConfiguration:
    properties:
      channels:
        type: integer
        docs: Number of audio channels.
      encoding:
        type: Encoding
        docs: Encoding format of the audio input, such as `linear16`.
      sample_rate:
        type: integer
        docs: >-
          Audio sample rate. Number of samples per second in the audio input,
          measured in Hertz.
    source:
      openapi: evi-asyncapi.json
  AudioInput:
    docs: When provided, the input is audio.
    properties:
      custom_session_id:
        type: optional<string>
        docs: >-
          Used to manage conversational state, correlate frontend and backend
          data, and persist conversations across EVI sessions.
      data:
        type: string
        docs: >-
          Base64 encoded audio input to insert into the conversation.


          The content of an Audio Input message is treated as the user’s speech
          to EVI and must be streamed continuously. Pre-recorded audio files are
          not supported.


          For optimal transcription quality, the audio data should be
          transmitted in small chunks.


          Hume recommends streaming audio with a buffer window of 20
          milliseconds (ms), or 100 milliseconds (ms) for web applications.
      type:
        type: literal<"audio_input">
        docs: >-
          The type of message sent through the socket; must be `audio_input` for
          our server to correctly identify and process it as an Audio Input
          message.


          This message is used for sending audio input data to EVI for
          processing and expression measurement. Audio data should be sent as a
          continuous stream, encoded in Base64.
    source:
      openapi: evi-asyncapi.json
  AudioOutput:
    docs: >-
      The type of message sent through the socket; for an Audio Output message,
      this must be `audio_output`.
    properties:
      custom_session_id:
        type: optional<string>
        docs: >-
          Used to manage conversational state, correlate frontend and backend
          data, and persist conversations across EVI sessions.
      data:
        type: string
        docs: >-
          Base64 encoded audio output. This encoded audio is transmitted to the
          client, where it can be decoded and played back as part of the user
          interaction.
      id:
        type: string
        docs: >-
          ID of the audio output. Allows the Audio Output message to be tracked
          and referenced.
      index:
        type: integer
        docs: Index of the chunk of audio relative to the whole audio segment.
      type:
        type: literal<"audio_output">
        docs: >-
          The type of message sent through the socket; for an Audio Output
          message, this must be `audio_output`.
    source:
      openapi: evi-asyncapi.json
  BuiltInTool:
    enum:
      - web_search
      - hang_up
    docs: >-
      Name of the built-in tool. Set to `web_search` to equip EVI with the
      built-in Web Search tool.
    source:
      openapi: evi-asyncapi.json
  BuiltinToolConfig:
    properties:
      fallback_content:
        type: optional<string>
        docs: >-
          Optional text passed to the supplemental LLM if the tool call fails.
          The LLM then uses this text to generate a response back to the user,
          ensuring continuity in the conversation.
      name:
        type: BuiltInTool
    source:
      openapi: evi-asyncapi.json
  ChatMessageToolResult:
    discriminated: false
    docs: Function call response from client.
    union:
      - type: ToolResponseMessage
      - type: ToolErrorMessage
    source:
      openapi: evi-asyncapi.json
    inline: true
  ChatMessage:
    properties:
      content:
        type: optional<string>
        docs: Transcript of the message.
      role:
        type: Role
        docs: Role of who is providing the message.
      tool_call:
        type: optional<ToolCallMessage>
        docs: Function call name and arguments.
      tool_result:
        type: optional<ChatMessageToolResult>
        docs: Function call response from client.
    source:
      openapi: evi-asyncapi.json
  ChatMetadata:
    docs: When provided, the output is a chat metadata message.
    properties:
      chat_group_id:
        type: string
        docs: >-
          ID of the Chat Group.


          Used to resume a Chat when passed in the
          [resumed_chat_group_id](/reference/empathic-voice-interface-evi/chat/chat#request.query.resumed_chat_group_id)
          query parameter of a subsequent connection request. This allows EVI to
          continue the conversation from where it left off within the Chat
          Group.


          Learn more about [supporting chat
          resumability](/docs/empathic-voice-interface-evi/faq#does-evi-support-chat-resumability)
          from the EVI FAQ.
      chat_id:
        type: string
        docs: >-
          ID of the Chat session. Allows the Chat session to be tracked and
          referenced.
      custom_session_id:
        type: optional<string>
        docs: >-
          Used to manage conversational state, correlate frontend and backend
          data, and persist conversations across EVI sessions.
      request_id:
        type: optional<string>
        docs: ID of the initiating request.
      type:
        type: literal<"chat_metadata">
        docs: >-
          The type of message sent through the socket; for a Chat Metadata
          message, this must be `chat_metadata`.


          The Chat Metadata message is the first message you receive after
          establishing a connection with EVI and contains important identifiers
          for the current Chat session.
    source:
      openapi: evi-asyncapi.json
  Context:
    properties:
      text:
        type: string
        docs: >-
          The context to be injected into the conversation. Helps inform the
          LLM's response by providing relevant information about the ongoing
          conversation.


          This text will be appended to the end of user messages based on the
          chosen persistence level. For example, if you want to remind EVI of
          its role as a helpful weather assistant, the context you insert will
          be appended to the end of user messages as `{Context: You are a
          helpful weather assistant}`.
      type:
        type: optional<ContextType>
        docs: >-
          The persistence level of the injected context. Specifies how long the
          injected context will remain active in the session.


          There are three possible context types:


          - **Persistent**: The context is appended to all user messages for the
          duration of the session.


          - **Temporary**: The context is appended only to the next user
          message.

           - **Editable**: The original context is updated to reflect the new context.

           If the type is not specified, it will default to `temporary`.
    source:
      openapi: evi-asyncapi.json
  ContextType:
    enum:
      - editable
      - persistent
      - temporary
    source:
      openapi: evi-asyncapi.json
  EmotionScores:
    properties:
      Admiration: double
      Adoration: double
      Aesthetic Appreciation: double
      Amusement: double
      Anger: double
      Anxiety: double
      Awe: double
      Awkwardness: double
      Boredom: double
      Calmness: double
      Concentration: double
      Confusion: double
      Contemplation: double
      Contempt: double
      Contentment: double
      Craving: double
      Desire: double
      Determination: double
      Disappointment: double
      Disgust: double
      Distress: double
      Doubt: double
      Ecstasy: double
      Embarrassment: double
      Empathic Pain: double
      Entrancement: double
      Envy: double
      Excitement: double
      Fear: double
      Guilt: double
      Horror: double
      Interest: double
      Joy: double
      Love: double
      Nostalgia: double
      Pain: double
      Pride: double
      Realization: double
      Relief: double
      Romance: double
      Sadness: double
      Satisfaction: double
      Shame: double
      Surprise (negative): double
      Surprise (positive): double
      Sympathy: double
      Tiredness: double
      Triumph: double
    source:
      openapi: evi-asyncapi.json
  Encoding:
    type: literal<"linear16">
  WebSocketError:
    docs: When provided, the output is an error message.
    properties:
      code:
        type: string
        docs: Error code. Identifies the type of error encountered.
      custom_session_id:
        type: optional<string>
        docs: >-
          Used to manage conversational state, correlate frontend and backend
          data, and persist conversations across EVI sessions.
      message:
        type: string
        docs: Detailed description of the error.
      request_id:
        type: optional<string>
        docs: ID of the initiating request.
      slug:
        type: string
        docs: >-
          Short, human-readable identifier and description for the error. See a
          complete list of error slugs on the [Errors
          page](/docs/resources/errors).
      type:
        type: literal<"error">
        docs: >-
          The type of message sent through the socket; for a Web Socket Error
          message, this must be `error`.


          This message indicates a disruption in the WebSocket connection, such
          as an unexpected disconnection, protocol error, or data transmission
          issue.
    source:
      openapi: evi-asyncapi.json
  ErrorLevel:
    type: literal<"warn">
  Inference:
    properties:
      prosody:
        type: optional<ProsodyInference>
        docs: >-
          Prosody model inference results.


          EVI uses the prosody model to measure 48 emotions related to speech
          and vocal characteristics within a given expression.
    source:
      openapi: evi-asyncapi.json
  MillisecondInterval:
    properties:
      begin:
        type: integer
        docs: Start time of the interval in milliseconds.
      end:
        type: integer
        docs: End time of the interval in milliseconds.
    source:
      openapi: evi-asyncapi.json
  PauseAssistantMessage:
    docs: >-
      Pause responses from EVI. Chat history is still saved and sent after
      resuming. 
    properties:
      custom_session_id:
        type: optional<string>
        docs: >-
          Used to manage conversational state, correlate frontend and backend
          data, and persist conversations across EVI sessions.
      type:
        type: literal<"pause_assistant_message">
        docs: >-
          The type of message sent through the socket; must be
          `pause_assistant_message` for our server to correctly identify and
          process it as a Pause Assistant message.


          Once this message is sent, EVI will not respond until a [Resume
          Assistant
          message](/reference/empathic-voice-interface-evi/chat/chat#send.ResumeAssistantMessage.type)
          is sent. When paused, EVI won’t respond, but transcriptions of your
          audio inputs will still be recorded.
    source:
      openapi: evi-asyncapi.json
  ProsodyInference:
    properties:
      scores:
        type: EmotionScores
        docs: >-
          The confidence scores for 48 emotions within the detected expression
          of an audio sample.


          Scores typically range from 0 to 1, with higher values indicating a
          stronger confidence level in the measured attribute.


          See our guide on [interpreting expression measurement
          results](/docs/expression-measurement/faq#how-do-i-interpret-my-results)
          to learn more.
    source:
      openapi: evi-asyncapi.json
  ResumeAssistantMessage:
    docs: >-
      Resume responses from EVI. Chat history sent while paused will now be
      sent. 
    properties:
      custom_session_id:
        type: optional<string>
        docs: >-
          Used to manage conversational state, correlate frontend and backend
          data, and persist conversations across EVI sessions.
      type:
        type: literal<"resume_assistant_message">
        docs: >-
          The type of message sent through the socket; must be
          `resume_assistant_message` for our server to correctly identify and
          process it as a Resume Assistant message.


          Upon resuming, if any audio input was sent during the pause, EVI will
          retain context from all messages sent but only respond to the last
          user message. (e.g., If you ask EVI two questions while paused and
          then send a `resume_assistant_message`, EVI will respond to the second
          question and have added the first question to its conversation
          context.)
    source:
      openapi: evi-asyncapi.json
  Role:
    enum:
      - assistant
      - system
      - user
      - all
      - tool
    source:
      openapi: evi-asyncapi.json
  SessionSettingsVariablesValue:
    discriminated: false
    union:
      - string
      - double
      - boolean
    source:
      openapi: evi-asyncapi.json
    inline: true
  SessionSettings:
    docs: Settings for this chat session.
    properties:
      audio:
        type: optional<AudioConfiguration>
        docs: >-
          Configuration details for the audio input used during the session.
          Ensures the audio is being correctly set up for processing.


          This optional field is only required when the audio input is encoded
          in PCM Linear 16 (16-bit, little-endian, signed PCM WAV data). For
          detailed instructions on how to configure session settings for PCM
          Linear 16 audio, please refer to the [Session Settings
          guide](/docs/empathic-voice-interface-evi/configuration/session-settings).
      builtin_tools:
        type: optional<list<BuiltinToolConfig>>
        docs: >-
          List of built-in tools to enable for the session.


          Tools are resources used by EVI to perform various tasks, such as
          searching the web or calling external APIs. Built-in tools, like web
          search, are natively integrated, while user-defined tools are created
          and invoked by the user. To learn more, see our [Tool Use
          Guide](/docs/empathic-voice-interface-evi/features/tool-use).


          Currently, the only built-in tool Hume provides is **Web Search**.
          When enabled, Web Search equips EVI with the ability to search the web
          for up-to-date information.
      context:
        type: optional<Context>
        docs: >-
          Allows developers to inject additional context into the conversation,
          which is appended to the end of user messages for the session.


          When included in a Session Settings message, the provided context can
          be used to remind the LLM of its role in every user message, prevent
          it from forgetting important details, or add new relevant information
          to the conversation.


          Set to `null` to disable context injection.
      custom_session_id:
        type: optional<string>
        docs: >-
          Unique identifier for the session. Used to manage conversational
          state, correlate frontend and backend data, and persist conversations
          across EVI sessions.


          If included, the response sent from Hume to your backend will include
          this ID. This allows you to correlate frontend users with their
          incoming messages.


          It is recommended to pass a `custom_session_id` if you are using a
          Custom Language Model. Please see our guide to [using a custom
          language
          model](/docs/empathic-voice-interface-evi/guides/custom-language-model)
          with EVI to learn more.
      language_model_api_key:
        type: optional<string>
        docs: >-
          Third party API key for the supplemental language model.


          When provided, EVI will use this key instead of Hume’s API key for the
          supplemental LLM. This allows you to bypass rate limits and utilize
          your own API key as needed.
      metadata:
        type: optional<map<string, unknown>>
      system_prompt:
        type: optional<string>
        docs: >-
          Instructions used to shape EVI’s behavior, responses, and style for
          the session.


          When included in a Session Settings message, the provided Prompt
          overrides the existing one specified in the EVI configuration. If no
          Prompt was defined in the configuration, this Prompt will be the one
          used for the session.


          You can use the Prompt to define a specific goal or role for EVI,
          specifying how it should act or what it should focus on during the
          conversation. For example, EVI can be instructed to act as a customer
          support representative, a fitness coach, or a travel advisor, each
          with its own set of behaviors and response styles.


          For help writing a system prompt, see our [Prompting
          Guide](/docs/empathic-voice-interface-evi/guides/prompting).
      tools:
        type: optional<list<Tool>>
        docs: >-
          List of user-defined tools to enable for the session.


          Tools are resources used by EVI to perform various tasks, such as
          searching the web or calling external APIs. Built-in tools, like web
          search, are natively integrated, while user-defined tools are created
          and invoked by the user. To learn more, see our [Tool Use
          Guide](/docs/empathic-voice-interface-evi/features/tool-use).
      type:
        type: literal<"session_settings">
        docs: >-
          The type of message sent through the socket; must be
          `session_settings` for our server to correctly identify and process it
          as a Session Settings message.


          Session settings are temporary and apply only to the current Chat
          session. These settings can be adjusted dynamically based on the
          requirements of each session to ensure optimal performance and user
          experience.


          For more information, please refer to the [Session Settings
          guide](/docs/empathic-voice-interface-evi/configuration/session-settings).
      variables:
        type: optional<map<string, SessionSettingsVariablesValue>>
        docs: >-
          This field allows you to assign values to dynamic variables referenced
          in your system prompt.


          Each key represents the variable name, and the corresponding value is
          the specific content you wish to assign to that variable within the
          session. While the values for variables can be strings, numbers, or
          booleans, the value will ultimately be converted to a string when
          injected into your system prompt.


          Using this field, you can personalize responses based on
          session-specific details. For more guidance, see our [guide on using
          dynamic
          variables](/docs/empathic-voice-interface-evi/features/dynamic-variables).
    source:
      openapi: evi-asyncapi.json
  Tool:
    properties:
      description:
        type: optional<string>
        docs: >-
          An optional description of what the tool does, used by the
          supplemental LLM to choose when and how to call the function.
      fallback_content:
        type: optional<string>
        docs: >-
          Optional text passed to the supplemental LLM if the tool call fails.
          The LLM then uses this text to generate a response back to the user,
          ensuring continuity in the conversation.
      name:
        type: string
        docs: Name of the user-defined tool to be enabled.
      parameters:
        type: string
        docs: >-
          Parameters of the tool. Is a stringified JSON schema.


          These parameters define the inputs needed for the tool’s execution,
          including the expected data type and description for each input field.
          Structured as a JSON schema, this format ensures the tool receives
          data in the expected format.
      type:
        type: ToolType
        docs: Type of tool. Set to `function` for user-defined tools.
    source:
      openapi: evi-asyncapi.json
  ToolCallMessage:
    docs: When provided, the output is a tool call.
    properties:
      custom_session_id:
        type: optional<string>
        docs: >-
          Used to manage conversational state, correlate frontend and backend
          data, and persist conversations across EVI sessions.
      name:
        type: string
        docs: Name of the tool called.
      parameters:
        type: string
        docs: >-
          Parameters of the tool.


          These parameters define the inputs needed for the tool’s execution,
          including the expected data type and description for each input field.
          Structured as a stringified JSON schema, this format ensures the tool
          receives data in the expected format.
      response_required:
        type: boolean
        docs: >-
          Indicates whether a response to the tool call is required from the
          developer, either in the form of a [Tool Response
          message](/reference/empathic-voice-interface-evi/chat/chat#send.ToolResponseMessage.type)
          or a [Tool Error
          message](/reference/empathic-voice-interface-evi/chat/chat#send.ToolErrorMessage.type).
      tool_call_id:
        type: string
        docs: >-
          The unique identifier for a specific tool call instance.


          This ID is used to track the request and response of a particular tool
          invocation, ensuring that the correct response is linked to the
          appropriate request.
      tool_type:
        type: optional<ToolType>
        docs: >-
          Type of tool called. Either `builtin` for natively implemented tools,
          like web search, or `function` for user-defined tools.
      type:
        type: literal<"tool_call">
        docs: >-
          The type of message sent through the socket; for a Tool Call message,
          this must be `tool_call`.


          This message indicates that the supplemental LLM has detected a need
          to invoke the specified tool.
    source:
      openapi: evi-asyncapi.json
  ToolErrorMessage:
    docs: When provided, the output is a function call error.
    properties:
      code:
        type: optional<string>
        docs: Error code. Identifies the type of error encountered.
      content:
        type: optional<string>
        docs: >-
          Optional text passed to the supplemental LLM in place of the tool call
          result. The LLM then uses this text to generate a response back to the
          user, ensuring continuity in the conversation if the tool errors.
      custom_session_id:
        type: optional<string>
        docs: >-
          Used to manage conversational state, correlate frontend and backend
          data, and persist conversations across EVI sessions.
      error:
        type: string
        docs: Error message from the tool call, not exposed to the LLM or user.
      level:
        type: optional<ErrorLevel>
        docs: >-
          Indicates the severity of an error; for a Tool Error message, this
          must be `warn` to signal an unexpected event.
      tool_call_id:
        type: string
        docs: >-
          The unique identifier for a specific tool call instance.


          This ID is used to track the request and response of a particular tool
          invocation, ensuring that the Tool Error message is linked to the
          appropriate tool call request. The specified `tool_call_id` must match
          the one received in the [Tool Call
          message](/reference/empathic-voice-interface-evi/chat/chat#receive.ToolCallMessage.type).
      tool_type:
        type: optional<ToolType>
        docs: >-
          Type of tool called. Either `builtin` for natively implemented tools,
          like web search, or `function` for user-defined tools.
      type:
        type: literal<"tool_error">
        docs: >-
          The type of message sent through the socket; for a Tool Error message,
          this must be `tool_error`.


          Upon receiving a [Tool Call
          message](/reference/empathic-voice-interface-evi/chat/chat#receive.ToolCallMessage.type)
          and failing to invoke the function, this message is sent to notify EVI
          of the tool's failure.
    source:
      openapi: evi-asyncapi.json
  ToolResponseMessage:
    docs: When provided, the output is a function call response.
    properties:
      content:
        type: string
        docs: >-
          Return value of the tool call. Contains the output generated by the
          tool to pass back to EVI.
      custom_session_id:
        type: optional<string>
        docs: >-
          Used to manage conversational state, correlate frontend and backend
          data, and persist conversations across EVI sessions.
      tool_call_id:
        type: string
        docs: >-
          The unique identifier for a specific tool call instance.


          This ID is used to track the request and response of a particular tool
          invocation, ensuring that the correct response is linked to the
          appropriate request. The specified `tool_call_id` must match the one
          received in the [Tool Call
          message](/reference/empathic-voice-interface-evi/chat/chat#receive.ToolCallMessage.tool_call_id).
      tool_name:
        type: optional<string>
        docs: >-
          Name of the tool.


          Include this optional field to help the supplemental LLM identify
          which tool generated the response. The specified `tool_name` must
          match the one received in the [Tool Call
          message](/reference/empathic-voice-interface-evi/chat/chat#receive.ToolCallMessage.type).
      tool_type:
        type: optional<ToolType>
        docs: >-
          Type of tool called. Either `builtin` for natively implemented tools,
          like web search, or `function` for user-defined tools.
      type:
        type: literal<"tool_response">
        docs: >-
          The type of message sent through the socket; for a Tool Response
          message, this must be `tool_response`.


          Upon receiving a [Tool Call
          message](/reference/empathic-voice-interface-evi/chat/chat#receive.ToolCallMessage.type)
          and successfully invoking the function, this message is sent to convey
          the result of the function call back to EVI.
    source:
      openapi: evi-asyncapi.json
  ToolType:
    enum:
      - builtin
      - function
    source:
      openapi: evi-asyncapi.json
  UserInput:
    docs: >-
      User text to insert into the conversation. Text sent through a User Input
      message is treated as the user's speech to EVI. EVI processes this input
      and provides a corresponding response.


      Expression measurement results are not available for User Input messages,
      as the prosody model relies on audio input and cannot process text alone.
    properties:
      custom_session_id:
        type: optional<string>
        docs: >-
          Used to manage conversational state, correlate frontend and backend
          data, and persist conversations across EVI sessions.
      text:
        type: string
        docs: >-
          User text to insert into the conversation. Text sent through a User
          Input message is treated as the user’s speech to EVI. EVI processes
          this input and provides a corresponding response.


          Expression measurement results are not available for User Input
          messages, as the prosody model relies on audio input and cannot
          process text alone.
      type:
        type: literal<"user_input">
        docs: >-
          The type of message sent through the socket; must be `user_input` for
          our server to correctly identify and process it as a User Input
          message.
    source:
      openapi: evi-asyncapi.json
  UserInterruption:
    docs: When provided, the output is an interruption.
    properties:
      custom_session_id:
        type: optional<string>
        docs: >-
          Used to manage conversational state, correlate frontend and backend
          data, and persist conversations across EVI sessions.
      time:
        type: integer
        docs: Unix timestamp of the detected user interruption.
      type:
        type: literal<"user_interruption">
        docs: >-
          The type of message sent through the socket; for a User Interruption
          message, this must be `user_interruption`.


          This message indicates the user has interrupted the assistant’s
          response. EVI detects the interruption in real-time and sends this
          message to signal the interruption event. This message allows the
          system to stop the current audio playback, clear the audio queue, and
          prepare to handle new user input.
    source:
      openapi: evi-asyncapi.json
  UserMessage:
    docs: When provided, the output is a user message.
    properties:
      custom_session_id:
        type: optional<string>
        docs: >-
          Used to manage conversational state, correlate frontend and backend
          data, and persist conversations across EVI sessions.
      from_text:
        type: boolean
        docs: >-
          Indicates if this message was inserted into the conversation as text
          from a [User
          Input](/reference/empathic-voice-interface-evi/chat/chat#send.UserInput.text)
          message.
      interim:
        type: boolean
        docs: >-
          Indicates whether this `UserMessage` contains an interim (unfinalized)
          transcript.


          - `true`: the transcript is provisional; words may be repeated or
          refined in subsequent `UserMessage` responses as additional audio is
          processed.

          - `false`: the transcript is final and complete.


          Interim transcripts are only sent when the
          [`verbose_transcription`](/reference/empathic-voice-interface-evi/chat/chat#request.query.verbose_transcription)
          query parameter is set to `true` in the initial handshake.
      message:
        type: ChatMessage
        docs: Transcript of the message.
      models:
        type: Inference
        docs: Inference model results.
      time:
        type: MillisecondInterval
        docs: Start and End time of user message.
      type:
        type: literal<"user_message">
        docs: >-
          The type of message sent through the socket; for a User Message, this
          must be `user_message`.


          This message contains both a transcript of the user’s input and the
          expression measurement predictions if the input was sent as an [Audio
          Input
          message](/reference/empathic-voice-interface-evi/chat/chat#send.AudioInput.type).
          Expression measurement predictions are not provided for a [User Input
          message](/reference/empathic-voice-interface-evi/chat/chat#send.UserInput.type),
          as the prosody model relies on audio input and cannot process text
          alone.
    source:
      openapi: evi-asyncapi.json
  JsonMessage:
    discriminated: false
    union:
      - type: AssistantEnd
      - type: AssistantMessage
      - type: ChatMetadata
      - type: WebSocketError
      - type: UserInterruption
      - type: UserMessage
      - type: ToolCallMessage
      - type: ToolResponseMessage
      - type: ToolErrorMessage
      - type: AssistantProsody
    source:
      openapi: evi-asyncapi.json
  HTTPValidationError:
    properties:
      detail:
        type: optional<list<ValidationError>>
    source:
      openapi: evi-openapi.json
  LanguageModelType:
    enum:
      - value: claude-3-7-sonnet-latest
        name: Claude37SonnetLatest
      - value: claude-3-5-sonnet-latest
        name: Claude35SonnetLatest
      - value: claude-3-5-haiku-latest
        name: Claude35HaikuLatest
      - value: claude-3-5-sonnet-20240620
        name: Claude35Sonnet20240620
      - value: claude-3-opus-20240229
        name: Claude3Opus20240229
      - value: claude-3-sonnet-20240229
        name: Claude3Sonnet20240229
      - value: claude-3-haiku-20240307
        name: Claude3Haiku20240307
      - value: claude-sonnet-4-20250514
        name: ClaudeSonnet420250514
      - value: us.anthropic.claude-3-5-haiku-20241022-v1:0
        name: UsAnthropicClaude35Haiku20241022V10
      - value: us.anthropic.claude-3-5-sonnet-20240620-v1:0
        name: UsAnthropicClaude35Sonnet20240620V10
      - value: us.anthropic.claude-3-haiku-20240307-v1:0
        name: UsAnthropicClaude3Haiku20240307V10
      - value: gpt-oss-120b
        name: GptOss120B
      - value: qwen-3-235b-a22b
        name: Qwen3235BA22B
      - value: qwen-3-235b-a22b-instruct-2507
        name: Qwen3235BA22BInstruct2507
      - value: qwen-3-235b-a22b-thinking-2507
        name: Qwen3235BA22BThinking2507
      - value: gemini-1.5-pro
        name: Gemini15Pro
      - value: gemini-1.5-flash
        name: Gemini15Flash
      - value: gemini-1.5-pro-002
        name: Gemini15Pro002
      - value: gemini-1.5-flash-002
        name: Gemini15Flash002
      - value: gemini-2.0-flash
        name: Gemini20Flash
      - value: gemini-2.5-flash
        name: Gemini25Flash
      - value: gemini-2.5-flash-preview-04-17
        name: Gemini25FlashPreview0417
      - value: gpt-4-turbo
        name: Gpt4Turbo
      - value: gpt-4-turbo-preview
        name: Gpt4TurboPreview
      - value: gpt-3.5-turbo-0125
        name: Gpt35Turbo0125
      - value: gpt-3.5-turbo
        name: Gpt35Turbo
      - value: gpt-4o
        name: Gpt4O
      - value: gpt-4o-mini
        name: Gpt4OMini
      - value: gpt-4.1
        name: Gpt41
      - value: gemma-7b-it
        name: Gemma7BIt
      - value: llama3-8b-8192
        name: Llama38B8192
      - value: llama3-70b-8192
        name: Llama370B8192
      - value: llama-3.1-70b-versatile
        name: Llama3170BVersatile
      - value: llama-3.3-70b-versatile
        name: Llama3370BVersatile
      - value: llama-3.1-8b-instant
        name: Llama318BInstant
      - value: moonshotai/kimi-k2-instruct
        name: MoonshotaiKimiK2Instruct
      - value: accounts/fireworks/models/mixtral-8x7b-instruct
        name: AccountsFireworksModelsMixtral8X7BInstruct
      - value: accounts/fireworks/models/llama-v3p1-405b-instruct
        name: AccountsFireworksModelsLlamaV3P1405BInstruct
      - value: accounts/fireworks/models/llama-v3p1-70b-instruct
        name: AccountsFireworksModelsLlamaV3P170BInstruct
      - value: accounts/fireworks/models/llama-v3p1-8b-instruct
        name: AccountsFireworksModelsLlamaV3P18BInstruct
      - sonar
      - value: sonar-pro
        name: SonarPro
      - sambanova
      - value: DeepSeek-R1-Distill-Llama-70B
        name: DeepSeekR1DistillLlama70B
      - value: Llama-4-Maverick-17B-128E-Instruct
        name: Llama4Maverick17B128EInstruct
      - value: Qwen3-32B
        name: Qwen332B
      - ellm
      - value: custom-language-model
        name: CustomLanguageModel
      - value: hume-evi-3-web-search
        name: HumeEvi3WebSearch
    source:
      openapi: evi-openapi.json
  ModelProviderEnum:
    enum:
      - GROQ
      - OPEN_AI
      - FIREWORKS
      - ANTHROPIC
      - CUSTOM_LANGUAGE_MODEL
      - GOOGLE
      - HUME_AI
      - AMAZON_BEDROCK
      - PERPLEXITY
      - SAMBANOVA
      - CEREBRAS
    source:
      openapi: evi-openapi.json
  SupportsToolUse:
    properties:
      model_resource: string
      supports_tool_use: boolean
    source:
      openapi: evi-openapi.json
  ValidationErrorLocItem:
    discriminated: false
    union:
      - string
      - integer
    source:
      openapi: evi-openapi.json
    inline: true
  ValidationError:
    properties:
      loc:
        type: list<ValidationErrorLocItem>
      msg: string
      type: string
    source:
      openapi: evi-openapi.json
  WebhookEventBase:
    docs: Represents the fields common to all webhook events.
    properties:
      chat_group_id:
        type: string
        docs: Unique ID of the **Chat Group** associated with the **Chat** session.
      chat_id:
        type: string
        docs: Unique ID of the **Chat** session.
      config_id:
        type: optional<string>
        docs: Unique ID of the EVI **Config** used for the session.
    source:
      openapi: evi-openapi.json
  WebhookEvent:
    discriminated: false
    union:
      - WebhookEventChatStarted
      - WebhookEventChatEnded
    source:
      openapi: evi-openapi.json
  WebhookEventChatEnded:
    properties:
      caller_number:
        type: optional<string>
        docs: >-
          Phone number of the caller in E.164 format (e.g., `+12223333333`).
          This field is included only if the Chat was created via the [Twilio
          phone calling](/docs/empathic-voice-interface-evi/phone-calling)
          integration.
      custom_session_id:
        type: optional<string>
        docs: >-
          User-defined session ID. Relevant only when employing a [custom
          language
          model](/docs/empathic-voice-interface-evi/custom-language-model) in
          the EVI Config.
      duration_seconds:
        type: integer
        docs: Total duration of the session in seconds.
      end_reason:
        type: WebhookEventChatStatus
        docs: Reason for the session's termination.
      end_time:
        type: integer
        docs: Unix timestamp (in milliseconds) indicating when the session ended.
      event_name:
        type: optional<literal<"chat_ended">>
        docs: Always `chat_ended`.
    extends:
      - WebhookEventBase
    source:
      openapi: evi-openapi.json
  WebhookEventChatStartType:
    enum:
      - new_chat_group
      - resumed_chat_group
    source:
      openapi: evi-openapi.json
  WebhookEventChatStarted:
    properties:
      caller_number:
        type: optional<string>
        docs: >-
          Phone number of the caller in E.164 format (e.g., `+12223333333`).
          This field is included only if the Chat was created via the [Twilio
          phone calling](/docs/empathic-voice-interface-evi/phone-calling)
          integration.
      chat_start_type:
        type: WebhookEventChatStartType
        docs: >-
          Indicates whether the chat is the first in a new Chat Group
          (`new_chat_group`) or the continuation of an existing chat group
          (`resumed_chat_group`).
      custom_session_id:
        type: optional<string>
        docs: >-
          User-defined session ID. Relevant only when employing a [custom
          language
          model](/docs/empathic-voice-interface-evi/custom-language-model) in
          the EVI Config.
      event_name:
        type: optional<literal<"chat_started">>
        docs: Always `chat_started`.
      start_time:
        type: integer
        docs: Unix timestamp (in milliseconds) indicating when the session started.
    extends:
      - WebhookEventBase
    source:
      openapi: evi-openapi.json
  WebhookEventChatStatus:
    enum:
      - ACTIVE
      - USER_ENDED
      - USER_TIMEOUT
      - INACTIVITY_TIMEOUT
      - MAX_DURATION_TIMEOUT
      - SILENCE_TIMEOUT
      - ERROR
    source:
      openapi: evi-openapi.json
  ErrorResponse:
    properties:
      error: optional<string>
      message: optional<string>
      code: optional<string>
    source:
      openapi: evi-openapi.json
  ReturnPagedUserDefinedTools:
    docs: A paginated list of user defined tool versions returned from the server
    properties:
      page_number:
        type: integer
        docs: >-
          The page number of the returned list.


          This value corresponds to the `page_number` parameter specified in the
          request. Pagination uses zero-based indexing.
      page_size:
        type: integer
        docs: >-
          The maximum number of items returned per page.


          This value corresponds to the `page_size` parameter specified in the
          request.
      total_pages:
        type: integer
        docs: The total number of pages in the collection.
      tools_page:
        docs: >-
          List of tools returned for the specified `page_number` and
          `page_size`.
        type: list<optional<ReturnUserDefinedTool>>
    source:
      openapi: evi-openapi.json
  ReturnUserDefinedToolToolType:
    enum:
      - BUILTIN
      - FUNCTION
    docs: >-
      Type of Tool. Either `BUILTIN` for natively implemented tools, like web
      search, or `FUNCTION` for user-defined tools.
    inline: true
    source:
      openapi: evi-openapi.json
  ReturnUserDefinedToolVersionType:
    enum:
      - FIXED
      - LATEST
    docs: >-
      Versioning method for a Tool. Either `FIXED` for using a fixed version
      number or `LATEST` for auto-updating to the latest version.
    inline: true
    source:
      openapi: evi-openapi.json
  ReturnUserDefinedTool:
    docs: A specific tool version returned from the server
    properties:
      tool_type:
        type: ReturnUserDefinedToolToolType
        docs: >-
          Type of Tool. Either `BUILTIN` for natively implemented tools, like
          web search, or `FUNCTION` for user-defined tools.
      id:
        type: string
        docs: Identifier for a Tool. Formatted as a UUID.
      version:
        type: integer
        docs: >-
          Version number for a Tool.


          Tools, Configs, Custom Voices, and Prompts are versioned. This
          versioning system supports iterative development, allowing you to
          progressively refine tools and revert to previous versions if needed.


          Version numbers are integer values representing different iterations
          of the Tool. Each update to the Tool increments its version number.
      version_type:
        type: ReturnUserDefinedToolVersionType
        docs: >-
          Versioning method for a Tool. Either `FIXED` for using a fixed version
          number or `LATEST` for auto-updating to the latest version.
      version_description:
        type: optional<string>
        docs: An optional description of the Tool version.
      name:
        type: string
        docs: Name applied to all versions of a particular Tool.
      created_on:
        type: long
        docs: >-
          Time at which the Tool was created. Measured in seconds since the Unix
          epoch.
      modified_on:
        type: long
        docs: >-
          Time at which the Tool was last modified. Measured in seconds since
          the Unix epoch.
      fallback_content:
        type: optional<string>
        docs: >-
          Optional text passed to the supplemental LLM in place of the tool call
          result. The LLM then uses this text to generate a response back to the
          user, ensuring continuity in the conversation if the Tool errors.
      description:
        type: optional<string>
        docs: >-
          An optional description of what the Tool does, used by the
          supplemental LLM to choose when and how to call the function.
      parameters:
        type: string
        docs: >-
          Stringified JSON defining the parameters used by this version of the
          Tool.


          These parameters define the inputs needed for the Tool’s execution,
          including the expected data type and description for each input field.
          Structured as a stringified JSON schema, this format ensures the tool
          receives data in the expected format.
    source:
      openapi: evi-openapi.json
  ReturnPagedPrompts:
    docs: A paginated list of prompt versions returned from the server
    properties:
      page_number:
        type: integer
        docs: >-
          The page number of the returned list.


          This value corresponds to the `page_number` parameter specified in the
          request. Pagination uses zero-based indexing.
      page_size:
        type: integer
        docs: >-
          The maximum number of items returned per page.


          This value corresponds to the `page_size` parameter specified in the
          request.
      total_pages:
        type: integer
        docs: The total number of pages in the collection.
      prompts_page:
        docs: >-
          List of prompts returned for the specified `page_number` and
          `page_size`.
        type: list<optional<ReturnPrompt>>
    source:
      openapi: evi-openapi.json
  ReturnPrompt:
    docs: A Prompt associated with this Config.
    properties:
      name:
        type: string
        docs: Name applied to all versions of a particular Prompt.
      id:
        type: string
        docs: Identifier for a Prompt. Formatted as a UUID.
      text:
        type: string
        docs: >-
          Instructions used to shape EVI’s behavior, responses, and style.


          You can use the Prompt to define a specific goal or role for EVI,
          specifying how it should act or what it should focus on during the
          conversation. For example, EVI can be instructed to act as a customer
          support representative, a fitness coach, or a travel advisor, each
          with its own set of behaviors and response styles.


          For help writing a system prompt, see our [Prompting
          Guide](/docs/speech-to-speech-evi/guides/prompting).
      version:
        type: integer
        docs: >-
          Version number for a Prompt.


          Prompts, Configs, Custom Voices, and Tools are versioned. This
          versioning system supports iterative development, allowing you to
          progressively refine prompts and revert to previous versions if
          needed.


          Version numbers are integer values representing different iterations
          of the Prompt. Each update to the Prompt increments its version
          number.
      version_type:
        type: ReturnPromptVersionType
        docs: >-
          Versioning method for a Prompt. Either `FIXED` for using a fixed
          version number or `LATEST` for auto-updating to the latest version.
      version_description:
        type: optional<string>
        docs: An optional description of the Prompt version.
      created_on:
        type: long
        docs: >-
          Time at which the Prompt was created. Measured in seconds since the
          Unix epoch.
      modified_on:
        type: long
        docs: >-
          Time at which the Prompt was last modified. Measured in seconds since
          the Unix epoch.
    source:
      openapi: evi-openapi.json
  ReturnPagedConfigs:
    docs: A paginated list of config versions returned from the server
    properties:
      page_number:
        type: optional<integer>
        docs: >-
          The page number of the returned list.


          This value corresponds to the `page_number` parameter specified in the
          request. Pagination uses zero-based indexing.
      page_size:
        type: optional<integer>
        docs: >-
          The maximum number of items returned per page.


          This value corresponds to the `page_size` parameter specified in the
          request.
      total_pages:
        type: integer
        docs: The total number of pages in the collection.
      configs_page:
        type: optional<list<ReturnConfig>>
        docs: >-
          List of configs returned for the specified `page_number` and
          `page_size`.
    source:
      openapi: evi-openapi.json
  ReturnConfig:
    docs: A specific config version returned from the server
    properties:
      name:
        type: optional<string>
        docs: Name applied to all versions of a particular Config.
      id:
        type: optional<string>
        docs: Identifier for a Config. Formatted as a UUID.
      version:
        type: optional<integer>
        docs: >-
          Version number for a Config.


          Configs, Prompts, Custom Voices, and Tools are versioned. This
          versioning system supports iterative development, allowing you to
          progressively refine configurations and revert to previous versions if
          needed.


          Version numbers are integer values representing different iterations
          of the Config. Each update to the Config increments its version
          number.
      language_model:
        type: optional<ReturnLanguageModel>
        docs: >-
          The supplemental language model associated with this Config.


          This model is used to generate longer, more detailed responses from
          EVI. Choosing an appropriate supplemental language model for your use
          case is crucial for generating fast, high-quality responses from EVI.
      builtin_tools:
        type: optional<list<optional<ReturnBuiltinTool>>>
        docs: List of built-in tools associated with this Config.
      evi_version:
        type: optional<string>
        docs: >-
          Specifies the EVI version to use. See our [EVI Version 
          Guide](/docs/speech-to-speech-evi/configuration/evi-version) for
          differences between versions.


          **We're officially sunsetting EVI versions 1 and 2 on August 30,
          2025**. To keep things running smoothly, be sure to [migrate to EVI
          3](/docs/speech-to-speech-evi/configuration/evi-version#migrating-to-evi-3)
          before then.
      timeouts: optional<ReturnTimeoutSpecs>
      event_messages: optional<ReturnEventMessageSpecs>
      ellm_model:
        type: optional<ReturnEllmModel>
        docs: >-
          The eLLM setup associated with this Config.


          Hume's eLLM (empathic Large Language Model) is a multimodal language
          model that takes into account both expression measures and language.
          The eLLM generates short, empathic language responses and guides
          text-to-speech (TTS) prosody.
      webhooks:
        type: optional<list<optional<ReturnWebhookSpec>>>
        docs: Map of webhooks associated with this config.
      version_description:
        type: optional<string>
        docs: An optional description of the Config version.
      created_on:
        type: optional<long>
        docs: >-
          Time at which the Config was created. Measured in seconds since the
          Unix epoch.
      modified_on:
        type: optional<long>
        docs: >-
          Time at which the Config was last modified. Measured in seconds since
          the Unix epoch.
      nudges: optional<ReturnNudgeSpec>
      voice: optional<unknown>
      prompt: optional<ReturnPrompt>
      tools:
        type: optional<list<optional<ReturnUserDefinedTool>>>
        docs: List of user-defined tools associated with this Config.
    source:
      openapi: evi-openapi.json
  ReturnPagedChatsPaginationDirection:
    enum:
      - ASC
      - DESC
    docs: >-
      Indicates the order in which the paginated results are presented, based on
      their creation date.


      It shows `ASC` for ascending order (chronological, with the oldest records
      first) or `DESC` for descending order (reverse-chronological, with the
      newest records first). This value corresponds to the `ascending_order`
      query parameter used in the request.
    inline: true
    source:
      openapi: evi-openapi.json
  ReturnPagedChats:
    docs: A paginated list of chats returned from the server
    properties:
      page_number:
        type: integer
        docs: >-
          The page number of the returned list.


          This value corresponds to the `page_number` parameter specified in the
          request. Pagination uses zero-based indexing.
      page_size:
        type: integer
        docs: >-
          The maximum number of items returned per page.


          This value corresponds to the `page_size` parameter specified in the
          request.
      total_pages:
        type: integer
        docs: The total number of pages in the collection.
      pagination_direction:
        type: ReturnPagedChatsPaginationDirection
        docs: >-
          Indicates the order in which the paginated results are presented,
          based on their creation date.


          It shows `ASC` for ascending order (chronological, with the oldest
          records first) or `DESC` for descending order (reverse-chronological,
          with the newest records first). This value corresponds to the
          `ascending_order` query parameter used in the request.
      chats_page:
        docs: >-
          List of Chats and their metadata returned for the specified
          `page_number` and `page_size`.
        type: list<ReturnChat>
    source:
      openapi: evi-openapi.json
  ReturnChatPagedEventsStatus:
    enum:
      - ACTIVE
      - USER_ENDED
      - USER_TIMEOUT
      - MAX_DURATION_TIMEOUT
      - INACTIVITY_TIMEOUT
      - ERROR
    docs: >-
      Indicates the current state of the chat. There are six possible statuses:


      - `ACTIVE`: The chat is currently active and ongoing.


      - `USER_ENDED`: The chat was manually ended by the user.


      - `USER_TIMEOUT`: The chat ended due to a user-defined timeout.


      - `MAX_DURATION_TIMEOUT`: The chat ended because it reached the maximum
      allowed duration.


      - `INACTIVITY_TIMEOUT`: The chat ended due to an inactivity timeout.


      - `ERROR`: The chat ended unexpectedly due to an error.
    inline: true
    source:
      openapi: evi-openapi.json
  ReturnChatPagedEventsPaginationDirection:
    enum:
      - ASC
      - DESC
    docs: >-
      Indicates the order in which the paginated results are presented, based on
      their creation date.


      It shows `ASC` for ascending order (chronological, with the oldest records
      first) or `DESC` for descending order (reverse-chronological, with the
      newest records first). This value corresponds to the `ascending_order`
      query parameter used in the request.
    inline: true
    source:
      openapi: evi-openapi.json
  ReturnChatPagedEvents:
    docs: >-
      A description of chat status with a paginated list of chat events returned
      from the server
    properties:
      id:
        type: string
        docs: Identifier for a Chat. Formatted as a UUID.
      chat_group_id:
        type: string
        docs: >-
          Identifier for the Chat Group. Any chat resumed from this Chat will
          have the same `chat_group_id`. Formatted as a UUID.
      status:
        type: ReturnChatPagedEventsStatus
        docs: >-
          Indicates the current state of the chat. There are six possible
          statuses:


          - `ACTIVE`: The chat is currently active and ongoing.


          - `USER_ENDED`: The chat was manually ended by the user.


          - `USER_TIMEOUT`: The chat ended due to a user-defined timeout.


          - `MAX_DURATION_TIMEOUT`: The chat ended because it reached the
          maximum allowed duration.


          - `INACTIVITY_TIMEOUT`: The chat ended due to an inactivity timeout.


          - `ERROR`: The chat ended unexpectedly due to an error.
      start_timestamp:
        type: long
        docs: >-
          Time at which the Chat started. Measured in seconds since the Unix
          epoch.
      end_timestamp:
        type: optional<long>
        docs: >-
          Time at which the Chat ended. Measured in seconds since the Unix
          epoch.
      pagination_direction:
        type: ReturnChatPagedEventsPaginationDirection
        docs: >-
          Indicates the order in which the paginated results are presented,
          based on their creation date.


          It shows `ASC` for ascending order (chronological, with the oldest
          records first) or `DESC` for descending order (reverse-chronological,
          with the newest records first). This value corresponds to the
          `ascending_order` query parameter used in the request.
      events_page:
        docs: List of Chat Events for the specified `page_number` and `page_size`.
        type: list<ReturnChatEvent>
      metadata:
        type: optional<string>
        docs: Stringified JSON with additional metadata about the chat.
      page_number:
        type: integer
        docs: >-
          The page number of the returned list.


          This value corresponds to the `page_number` parameter specified in the
          request. Pagination uses zero-based indexing.
      page_size:
        type: integer
        docs: >-
          The maximum number of items returned per page.


          This value corresponds to the `page_size` parameter specified in the
          request.
      total_pages:
        type: integer
        docs: The total number of pages in the collection.
      config: optional<ReturnConfigSpec>
    source:
      openapi: evi-openapi.json
  ReturnChatAudioReconstructionStatus:
    enum:
      - QUEUED
      - IN_PROGRESS
      - COMPLETE
      - ERROR
      - CANCELLED
    docs: >-
      Indicates the current state of the audio reconstruction job. There are
      five possible statuses:


      - `QUEUED`: The reconstruction job is waiting to be processed.


      - `IN_PROGRESS`: The reconstruction is currently being processed.


      - `COMPLETE`: The audio reconstruction is finished and ready for download.


      - `ERROR`: An error occurred during the reconstruction process.


      - `CANCELED`: The reconstruction job has been canceled.
    inline: true
    source:
      openapi: evi-openapi.json
  ReturnChatAudioReconstruction:
    docs: >-
      List of chat audio reconstructions returned for the specified page number
      and page size.
    properties:
      id:
        type: string
        docs: Identifier for the chat. Formatted as a UUID.
      user_id:
        type: string
        docs: Identifier for the user that owns this chat. Formatted as a UUID.
      status:
        type: ReturnChatAudioReconstructionStatus
        docs: >-
          Indicates the current state of the audio reconstruction job. There are
          five possible statuses:


          - `QUEUED`: The reconstruction job is waiting to be processed.


          - `IN_PROGRESS`: The reconstruction is currently being processed.


          - `COMPLETE`: The audio reconstruction is finished and ready for
          download.


          - `ERROR`: An error occurred during the reconstruction process.


          - `CANCELED`: The reconstruction job has been canceled.
      filename:
        type: optional<string>
        docs: Name of the chat audio reconstruction file.
      modified_at:
        type: optional<long>
        docs: >-
          The timestamp of the most recent status change for this audio
          reconstruction, formatted milliseconds since the Unix epoch.
      signed_audio_url:
        type: optional<string>
        docs: Signed URL used to download the chat audio reconstruction file.
      signed_url_expiration_timestamp_millis:
        type: optional<long>
        docs: >-
          The timestamp when the signed URL will expire, formatted as a Unix
          epoch milliseconds.
    source:
      openapi: evi-openapi.json
  ReturnPagedChatGroupsPaginationDirection:
    enum:
      - ASC
      - DESC
    docs: >-
      Indicates the order in which the paginated results are presented, based on
      their creation date.


      It shows `ASC` for ascending order (chronological, with the oldest records
      first) or `DESC` for descending order (reverse-chronological, with the
      newest records first). This value corresponds to the `ascending_order`
      query parameter used in the request.
    inline: true
    source:
      openapi: evi-openapi.json
  ReturnPagedChatGroups:
    docs: A paginated list of chat_groups returned from the server
    properties:
      page_number:
        type: integer
        docs: >-
          The page number of the returned list.


          This value corresponds to the `page_number` parameter specified in the
          request. Pagination uses zero-based indexing.
      page_size:
        type: integer
        docs: >-
          The maximum number of items returned per page.


          This value corresponds to the `page_size` parameter specified in the
          request.
      total_pages:
        type: integer
        docs: The total number of pages in the collection.
      pagination_direction:
        type: ReturnPagedChatGroupsPaginationDirection
        docs: >-
          Indicates the order in which the paginated results are presented,
          based on their creation date.


          It shows `ASC` for ascending order (chronological, with the oldest
          records first) or `DESC` for descending order (reverse-chronological,
          with the newest records first). This value corresponds to the
          `ascending_order` query parameter used in the request.
      chat_groups_page:
        docs: >-
          List of Chat Groups and their metadata returned for the specified
          `page_number` and `page_size`.
        type: list<ReturnChatGroup>
    source:
      openapi: evi-openapi.json
  ReturnChatGroupPagedChatsPaginationDirection:
    enum:
      - ASC
      - DESC
    docs: >-
      Indicates the order in which the paginated results are presented, based on
      their creation date.


      It shows `ASC` for ascending order (chronological, with the oldest records
      first) or `DESC` for descending order (reverse-chronological, with the
      newest records first). This value corresponds to the `ascending_order`
      query parameter used in the request.
    inline: true
    source:
      openapi: evi-openapi.json
  ReturnChatGroupPagedChats:
    docs: >-
      A description of chat_group and its status with a paginated list of each
      chat in the chat_group
    properties:
      id:
        type: string
        docs: >-
          Identifier for the Chat Group. Any Chat resumed from this Chat Group
          will have the same `chat_group_id`. Formatted as a UUID.
      first_start_timestamp:
        type: long
        docs: >-
          Time at which the first Chat in this Chat Group was created. Measured
          in seconds since the Unix epoch.
      most_recent_start_timestamp:
        type: long
        docs: >-
          Time at which the most recent Chat in this Chat Group was created.
          Measured in seconds since the Unix epoch.
      num_chats:
        type: integer
        docs: The total number of Chats associated with this Chat Group.
      page_number:
        type: integer
        docs: >-
          The page number of the returned list.


          This value corresponds to the `page_number` parameter specified in the
          request. Pagination uses zero-based indexing.
      page_size:
        type: integer
        docs: >-
          The maximum number of items returned per page.


          This value corresponds to the `page_size` parameter specified in the
          request.
      total_pages:
        type: integer
        docs: The total number of pages in the collection.
      pagination_direction:
        type: ReturnChatGroupPagedChatsPaginationDirection
        docs: >-
          Indicates the order in which the paginated results are presented,
          based on their creation date.


          It shows `ASC` for ascending order (chronological, with the oldest
          records first) or `DESC` for descending order (reverse-chronological,
          with the newest records first). This value corresponds to the
          `ascending_order` query parameter used in the request.
      chats_page:
        docs: List of Chats for the specified `page_number` and `page_size`.
        type: list<ReturnChat>
      active:
        type: optional<boolean>
        docs: >-
          Denotes whether there is an active Chat associated with this Chat
          Group.
    source:
      openapi: evi-openapi.json
  ReturnChatGroupPagedEventsPaginationDirection:
    enum:
      - ASC
      - DESC
    docs: >-
      Indicates the order in which the paginated results are presented, based on
      their creation date.


      It shows `ASC` for ascending order (chronological, with the oldest records
      first) or `DESC` for descending order (reverse-chronological, with the
      newest records first). This value corresponds to the `ascending_order`
      query parameter used in the request.
    inline: true
    source:
      openapi: evi-openapi.json
  ReturnChatGroupPagedEvents:
    docs: >-
      A paginated list of chat events that occurred across chats in this
      chat_group from the server
    properties:
      id:
        type: string
        docs: >-
          Identifier for the Chat Group. Any Chat resumed from this Chat Group
          will have the same `chat_group_id`. Formatted as a UUID.
      page_number:
        type: integer
        docs: >-
          The page number of the returned list.


          This value corresponds to the `page_number` parameter specified in the
          request. Pagination uses zero-based indexing.
      page_size:
        type: integer
        docs: >-
          The maximum number of items returned per page.


          This value corresponds to the `page_size` parameter specified in the
          request.
      total_pages:
        type: integer
        docs: The total number of pages in the collection.
      pagination_direction:
        type: ReturnChatGroupPagedEventsPaginationDirection
        docs: >-
          Indicates the order in which the paginated results are presented,
          based on their creation date.


          It shows `ASC` for ascending order (chronological, with the oldest
          records first) or `DESC` for descending order (reverse-chronological,
          with the newest records first). This value corresponds to the
          `ascending_order` query parameter used in the request.
      events_page:
        docs: List of Chat Events for the specified `page_number` and `page_size`.
        type: list<ReturnChatEvent>
    source:
      openapi: evi-openapi.json
  ReturnChatGroupPagedAudioReconstructionsPaginationDirection:
    enum:
      - ASC
      - DESC
    docs: >-
      Indicates the order in which the paginated results are presented, based on
      their creation date.


      It shows `ASC` for ascending order (chronological, with the oldest records
      first) or `DESC` for descending order (reverse-chronological, with the
      newest records first). This value corresponds to the `ascending_order`
      query parameter used in the request.
    inline: true
    source:
      openapi: evi-openapi.json
  ReturnChatGroupPagedAudioReconstructions:
    docs: A paginated list of chat reconstructions for a particular chatgroup
    properties:
      id:
        type: string
        docs: Identifier for the chat group. Formatted as a UUID.
      user_id:
        type: string
        docs: Identifier for the user that owns this chat. Formatted as a UUID.
      num_chats:
        type: integer
        docs: Total number of chats in this chatgroup
      page_number:
        type: integer
        docs: >-
          The page number of the returned list.


          This value corresponds to the `page_number` parameter specified in the
          request. Pagination uses zero-based indexing.
      page_size:
        type: integer
        docs: >-
          The maximum number of items returned per page.


          This value corresponds to the `page_size` parameter specified in the
          request.
      total_pages:
        type: integer
        docs: The total number of pages in the collection.
      pagination_direction:
        type: ReturnChatGroupPagedAudioReconstructionsPaginationDirection
        docs: >-
          Indicates the order in which the paginated results are presented,
          based on their creation date.


          It shows `ASC` for ascending order (chronological, with the oldest
          records first) or `DESC` for descending order (reverse-chronological,
          with the newest records first). This value corresponds to the
          `ascending_order` query parameter used in the request.
      audio_reconstructions_page:
        docs: >-
          List of chat audio reconstructions returned for the specified page
          number and page size.
        type: list<ReturnChatAudioReconstruction>
    source:
      openapi: evi-openapi.json
  ReturnPromptVersionType:
    enum:
      - FIXED
      - LATEST
    docs: >-
      Versioning method for a Prompt. Either `FIXED` for using a fixed version
      number or `LATEST` for auto-updating to the latest version.
    inline: true
    source:
      openapi: evi-openapi.json
  PostedConfigPromptSpec:
    docs: >-
      Identifies which prompt to use in a a config OR how to create a new prompt
      to use in the config
    properties:
      id:
        type: optional<string>
        docs: Identifier for a Prompt. Formatted as a UUID.
      version:
        type: optional<integer>
        docs: >-
          Version number for a Prompt. Version numbers should be integers. The
          combination of configId and version number is unique.
      text:
        type: optional<string>
        docs: Text used to create a new prompt for a particular config.
    source:
      openapi: evi-openapi.json
  PostedLanguageModel:
    docs: A LanguageModel to be posted to the server
    properties:
      model_provider:
        type: optional<ModelProviderEnum>
        docs: The provider of the supplemental language model.
      model_resource:
        type: optional<LanguageModelType>
        docs: String that specifies the language model to use with `model_provider`.
      temperature:
        type: optional<float>
        docs: >-
          The model temperature, with values between 0 to 1 (inclusive).


          Controls the randomness of the LLM’s output, with values closer to 0
          yielding focused, deterministic responses and values closer to 1
          producing more creative, diverse responses.
    source:
      openapi: evi-openapi.json
  PostedEllmModel:
    docs: A eLLM model configuration to be posted to the server
    properties:
      allow_short_responses:
        type: optional<boolean>
        docs: |-
          Boolean indicating if the eLLM is allowed to generate short responses.

          If omitted, short responses from the eLLM are enabled by default.
    source:
      openapi: evi-openapi.json
  PostedUserDefinedToolSpec:
    docs: A specific tool identifier to be posted to the server
    properties:
      id:
        type: string
        docs: Identifier for a Tool. Formatted as a UUID.
      version:
        type: optional<integer>
        docs: >-
          Version number for a Tool.


          Tools, Configs, Custom Voices, and Prompts are versioned. This
          versioning system supports iterative development, allowing you to
          progressively refine tools and revert to previous versions if needed.


          Version numbers are integer values representing different iterations
          of the Tool. Each update to the Tool increments its version number.
    source:
      openapi: evi-openapi.json
  PostedBuiltinToolName:
    enum:
      - web_search
      - hang_up
    docs: >-
      Name of the built-in tool to use. Hume supports the following built-in
      tools:


      - **web_search:** enables EVI to search the web for up-to-date information
      when applicable.

      - **hang_up:** closes the WebSocket connection when appropriate (e.g.,
      after detecting a farewell in the conversation).


      For more information, see our guide on [using built-in
      tools](/docs/speech-to-speech-evi/features/tool-use#using-built-in-tools).
    inline: true
    source:
      openapi: evi-openapi.json
  PostedBuiltinTool:
    docs: A configuration of a built-in tool to be posted to the server
    properties:
      name:
        type: PostedBuiltinToolName
        docs: >-
          Name of the built-in tool to use. Hume supports the following built-in
          tools:


          - **web_search:** enables EVI to search the web for up-to-date
          information when applicable.

          - **hang_up:** closes the WebSocket connection when appropriate (e.g.,
          after detecting a farewell in the conversation).


          For more information, see our guide on [using built-in
          tools](/docs/speech-to-speech-evi/features/tool-use#using-built-in-tools).
      fallback_content:
        type: optional<string>
        docs: >-
          Optional text passed to the supplemental LLM in place of the tool call
          result. The LLM then uses this text to generate a response back to the
          user, ensuring continuity in the conversation if the Tool errors.
    source:
      openapi: evi-openapi.json
  PostedEventMessageSpecs:
    docs: >-
      Collection of event messages returned by the server.


      Event messages are sent by the server when specific events occur during a
      chat session. These messages are used to configure behaviors for EVI, such
      as controlling how EVI starts a new conversation.
    properties:
      on_new_chat:
        type: optional<PostedEventMessageSpec>
        docs: >-
          Specifies the initial message EVI provides when a new chat is started,
          such as a greeting or welcome message.
      on_inactivity_timeout:
        type: optional<PostedEventMessageSpec>
        docs: >-
          Specifies the message EVI provides when the chat is about to be
          disconnected due to a user inactivity timeout, such as a message
          mentioning a lack of user input for a period of time.


          Enabling an inactivity message allows developers to use this message
          event for "checking in" with the user if they are not responding to
          see if they are still active.


          If the user does not respond in the number of seconds specified in the
          `inactivity_timeout` field, then EVI will say the message and the user
          has 15 seconds to respond. If they respond in time, the conversation
          will continue; if not, the conversation will end.


          However, if the inactivity message is not enabled, then reaching the
          inactivity timeout will immediately end the connection.
      on_max_duration_timeout:
        type: optional<PostedEventMessageSpec>
        docs: >-
          Specifies the message EVI provides when the chat is disconnected due
          to reaching the maximum chat duration, such as a message mentioning
          the time limit for the chat has been reached.
    source:
      openapi: evi-openapi.json
  PostedNudgeSpec:
    docs: A nudge specification posted to the server
    properties:
      enabled:
        type: optional<boolean>
        docs: >-
          If true, EVI will 'nudge' the user to speak after a determined
          interval of silence.
      interval_secs:
        type: optional<integer>
        docs: The interval of inactivity (in seconds) before a nudge is triggered.
    source:
      openapi: evi-openapi.json
  PostedTimeoutSpecsInactivity:
    docs: >-
      Specifies the duration of user inactivity (in seconds) after which the EVI
      WebSocket connection will be automatically disconnected. Default is 600
      seconds (10 minutes).


      Accepts a minimum value of 30 seconds and a maximum value of 1,800
      seconds.
    properties:
      enabled:
        type: boolean
        docs: >-
          Boolean indicating if this timeout is enabled.


          If set to false, EVI will not timeout due to a specified duration of
          user inactivity being reached. However, the conversation will
          eventually disconnect after 1,800 seconds (30 minutes), which is the
          maximum WebSocket duration limit for EVI.
      duration_secs:
        type: optional<integer>
        docs: >-
          Duration in seconds for the timeout (e.g. 600 seconds represents 10
          minutes).
    source:
      openapi: evi-openapi.json
    inline: true
  PostedTimeoutSpecsMaxDuration:
    docs: >-
      Specifies the maximum allowed duration (in seconds) for an EVI WebSocket
      connection before it is automatically disconnected. Default is 1,800
      seconds (30 minutes).


      Accepts a minimum value of 30 seconds and a maximum value of 1,800
      seconds.
    properties:
      enabled:
        type: boolean
        docs: >-
          Boolean indicating if this timeout is enabled.


          If set to false, EVI will not timeout due to a specified maximum
          duration being reached. However, the conversation will eventually
          disconnect after 1,800 seconds (30 minutes), which is the maximum
          WebSocket duration limit for EVI.
      duration_secs:
        type: optional<integer>
        docs: >-
          Duration in seconds for the timeout (e.g. 600 seconds represents 10
          minutes).
    source:
      openapi: evi-openapi.json
    inline: true
  PostedTimeoutSpecs:
    docs: >-
      Collection of timeout specifications returned by the server.


      Timeouts are sent by the server when specific time-based events occur
      during a chat session. These specifications set the inactivity timeout and
      the maximum duration an EVI WebSocket connection can stay open before it
      is automatically disconnected.
    properties:
      inactivity:
        type: optional<PostedTimeoutSpecsInactivity>
        docs: >-
          Specifies the duration of user inactivity (in seconds) after which the
          EVI WebSocket connection will be automatically disconnected. Default
          is 600 seconds (10 minutes).


          Accepts a minimum value of 30 seconds and a maximum value of 1,800
          seconds.
      max_duration:
        type: optional<PostedTimeoutSpecsMaxDuration>
        docs: >-
          Specifies the maximum allowed duration (in seconds) for an EVI
          WebSocket connection before it is automatically disconnected. Default
          is 1,800 seconds (30 minutes).


          Accepts a minimum value of 30 seconds and a maximum value of 1,800
          seconds.
    source:
      openapi: evi-openapi.json
  PostedWebhookEventType:
    enum:
      - chat_started
      - chat_ended
    docs: Events this URL is subscribed to
    inline: true
    source:
      openapi: evi-openapi.json
  PostedWebhookSpec:
    docs: URL and settings for a specific webhook to be posted to the server
    properties:
      url:
        type: string
        docs: >-
          The URL where event payloads will be sent. This must be a valid https
          URL to ensure secure communication. The server at this URL must accept
          POST requests with a JSON payload.
      events:
        docs: >-
          The list of events the specified URL is subscribed to. 


          See our [webhooks
          guide](/docs/speech-to-speech-evi/configuration/build-a-configuration#supported-events)
          for more information on supported events.
        type: list<PostedWebhookEventType>
    source:
      openapi: evi-openapi.json
  ReturnLanguageModel:
    docs: A specific LanguageModel
    properties:
      model_provider:
        type: optional<ModelProviderEnum>
        docs: The provider of the supplemental language model.
      model_resource:
        type: optional<LanguageModelType>
        docs: String that specifies the language model to use with `model_provider`.
      temperature:
        type: optional<float>
        docs: >-
          The model temperature, with values between 0 to 1 (inclusive).


          Controls the randomness of the LLM’s output, with values closer to 0
          yielding focused, deterministic responses and values closer to 1
          producing more creative, diverse responses.
    source:
      openapi: evi-openapi.json
  ReturnEllmModel:
    docs: A specific eLLM Model configuration
    properties:
      allow_short_responses:
        type: boolean
        docs: |-
          Boolean indicating if the eLLM is allowed to generate short responses.

          If omitted, short responses from the eLLM are enabled by default.
    source:
      openapi: evi-openapi.json
  ReturnBuiltinToolToolType:
    enum:
      - BUILTIN
      - FUNCTION
    docs: >-
      Type of Tool. Either `BUILTIN` for natively implemented tools, like web
      search, or `FUNCTION` for user-defined tools.
    inline: true
    source:
      openapi: evi-openapi.json
  ReturnBuiltinTool:
    docs: A specific builtin tool version returned from the server
    properties:
      tool_type:
        type: ReturnBuiltinToolToolType
        docs: >-
          Type of Tool. Either `BUILTIN` for natively implemented tools, like
          web search, or `FUNCTION` for user-defined tools.
      name:
        type: string
        docs: Name applied to all versions of a particular Tool.
      fallback_content:
        type: optional<string>
        docs: >-
          Optional text passed to the supplemental LLM in place of the tool call
          result. The LLM then uses this text to generate a response back to the
          user, ensuring continuity in the conversation if the Tool errors.
    source:
      openapi: evi-openapi.json
  ReturnEventMessageSpecs:
    docs: >-
      Collection of event messages returned by the server.


      Event messages are sent by the server when specific events occur during a
      chat session. These messages are used to configure behaviors for EVI, such
      as controlling how EVI starts a new conversation.
    properties:
      on_new_chat:
        type: optional<ReturnEventMessageSpec>
        docs: >-
          Specifies the initial message EVI provides when a new chat is started,
          such as a greeting or welcome message.
      on_inactivity_timeout:
        type: optional<ReturnEventMessageSpec>
        docs: >-
          Specifies the message EVI provides when the chat is about to be
          disconnected due to a user inactivity timeout, such as a message
          mentioning a lack of user input for a period of time.


          Enabling an inactivity message allows developers to use this message
          event for "checking in" with the user if they are not responding to
          see if they are still active.


          If the user does not respond in the number of seconds specified in the
          `inactivity_timeout` field, then EVI will say the message and the user
          has 15 seconds to respond. If they respond in time, the conversation
          will continue; if not, the conversation will end.


          However, if the inactivity message is not enabled, then reaching the
          inactivity timeout will immediately end the connection.
      on_max_duration_timeout:
        type: optional<ReturnEventMessageSpec>
        docs: >-
          Specifies the message EVI provides when the chat is disconnected due
          to reaching the maximum chat duration, such as a message mentioning
          the time limit for the chat has been reached.
    source:
      openapi: evi-openapi.json
  ReturnTimeoutSpecs:
    docs: >-
      Collection of timeout specifications returned by the server.


      Timeouts are sent by the server when specific time-based events occur
      during a chat session. These specifications set the inactivity timeout and
      the maximum duration an EVI WebSocket connection can stay open before it
      is automatically disconnected.
    properties:
      inactivity:
        type: ReturnTimeoutSpec
        docs: >-
          Specifies the duration of user inactivity (in seconds) after which the
          EVI WebSocket connection will be automatically disconnected. Default
          is 600 seconds (10 minutes).


          Accepts a minimum value of 30 seconds and a maximum value of 1,800
          seconds.
      max_duration:
        type: ReturnTimeoutSpec
        docs: >-
          Specifies the maximum allowed duration (in seconds) for an EVI
          WebSocket connection before it is automatically disconnected. Default
          is 1,800 seconds (30 minutes).


          Accepts a minimum value of 30 seconds and a maximum value of 1,800
          seconds.
    source:
      openapi: evi-openapi.json
  ReturnNudgeSpec:
    docs: A specific nudge configuration returned from the server
    properties:
      enabled:
        type: boolean
        docs: EVI will nudge user after inactivity
      interval_secs:
        type: optional<integer>
        docs: Time interval in seconds after which the nudge will be sent.
    source:
      openapi: evi-openapi.json
  ReturnWebhookEventType:
    enum:
      - chat_started
      - chat_ended
    docs: Events this URL is subscribed to
    inline: true
    source:
      openapi: evi-openapi.json
  ReturnWebhookSpec:
    docs: Collection of webhook URL endpoints to be returned from the server
    properties:
      url:
        type: string
        docs: >-
          The URL where event payloads will be sent. This must be a valid https
          URL to ensure secure communication. The server at this URL must accept
          POST requests with a JSON payload.
      events:
        docs: >-
          The list of events the specified URL is subscribed to. 


          See our [webhooks
          guide](/docs/speech-to-speech-evi/configuration/build-a-configuration#supported-events)
          for more information on supported events.
        type: list<ReturnWebhookEventType>
    source:
      openapi: evi-openapi.json
  ReturnChatStatus:
    enum:
      - ACTIVE
      - USER_ENDED
      - USER_TIMEOUT
      - MAX_DURATION_TIMEOUT
      - INACTIVITY_TIMEOUT
      - ERROR
    docs: >-
      Indicates the current state of the chat. There are six possible statuses:


      - `ACTIVE`: The chat is currently active and ongoing.


      - `USER_ENDED`: The chat was manually ended by the user.


      - `USER_TIMEOUT`: The chat ended due to a user-defined timeout.


      - `MAX_DURATION_TIMEOUT`: The chat ended because it reached the maximum
      allowed duration.


      - `INACTIVITY_TIMEOUT`: The chat ended due to an inactivity timeout.


      - `ERROR`: The chat ended unexpectedly due to an error.
    inline: true
    source:
      openapi: evi-openapi.json
  ReturnChat:
    docs: A description of chat and its status
    properties:
      id:
        type: string
        docs: Identifier for a Chat. Formatted as a UUID.
      chat_group_id:
        type: string
        docs: >-
          Identifier for the Chat Group. Any chat resumed from this Chat will
          have the same `chat_group_id`. Formatted as a UUID.
      status:
        type: ReturnChatStatus
        docs: >-
          Indicates the current state of the chat. There are six possible
          statuses:


          - `ACTIVE`: The chat is currently active and ongoing.


          - `USER_ENDED`: The chat was manually ended by the user.


          - `USER_TIMEOUT`: The chat ended due to a user-defined timeout.


          - `MAX_DURATION_TIMEOUT`: The chat ended because it reached the
          maximum allowed duration.


          - `INACTIVITY_TIMEOUT`: The chat ended due to an inactivity timeout.


          - `ERROR`: The chat ended unexpectedly due to an error.
      start_timestamp:
        type: long
        docs: >-
          Time at which the Chat started. Measured in seconds since the Unix
          epoch.
      end_timestamp:
        type: optional<long>
        docs: >-
          Time at which the Chat ended. Measured in seconds since the Unix
          epoch.
      event_count:
        type: optional<long>
        docs: The total number of events currently in this chat.
      metadata:
        type: optional<string>
        docs: Stringified JSON with additional metadata about the chat.
      config: optional<ReturnConfigSpec>
    source:
      openapi: evi-openapi.json
  ReturnChatEventRole:
    enum:
      - USER
      - AGENT
      - SYSTEM
      - TOOL
    docs: >-
      The role of the entity which generated the Chat Event. There are four
      possible values:

      - `USER`: The user, capable of sending user messages and interruptions.

      - `AGENT`: The assistant, capable of sending agent messages.

      - `SYSTEM`: The backend server, capable of transmitting errors.

      - `TOOL`: The function calling mechanism.
    inline: true
    source:
      openapi: evi-openapi.json
  ReturnChatEventType:
    enum:
      - FUNCTION_CALL
      - FUNCTION_CALL_RESPONSE
      - CHAT_END_MESSAGE
      - AGENT_MESSAGE
      - SYSTEM_PROMPT
      - USER_RECORDING_START_MESSAGE
      - RESUME_ONSET
      - USER_INTERRUPTION
      - CHAT_START_MESSAGE
      - PAUSE_ONSET
      - USER_MESSAGE
    docs: >-
      Type of Chat Event. There are eleven Chat Event types:

      - `SYSTEM_PROMPT`: The system prompt used to initialize the session.

      - `CHAT_START_MESSAGE`: Marks the beginning of the chat session.

      - `USER_RECORDING_START_MESSAGE`: Marks when the client began streaming
      audio and the start of audio processing.

      - `USER_MESSAGE`: A message sent by the user.

      - `USER_INTERRUPTION`: A user-initiated interruption while the assistant
      is speaking.

      - `AGENT_MESSAGE`: A response generated by the assistant.

      - `FUNCTION_CALL`: A record of a tool invocation by the assistant.

      - `FUNCTION_CALL_RESPONSE`: The result of a previously invoked function or
      tool.

      - `PAUSE_ONSET`: Marks when the client sent a `pause_assistant_message` to
      pause the assistant.

      - `RESUME_ONSET`: Marks when the client sent a `resume_assistant_message`
      to resume the assistant.

      - `CHAT_END_MESSAGE`: Indicates the end of the chat session.
    inline: true
    source:
      openapi: evi-openapi.json
  ReturnChatEvent:
    docs: A description of a single event in a chat returned from the server
    properties:
      id:
        type: string
        docs: Identifier for a Chat Event. Formatted as a UUID.
      chat_id:
        type: string
        docs: Identifier for the Chat this event occurred in. Formatted as a UUID.
      timestamp:
        type: long
        docs: >-
          Time at which the Chat Event occurred. Measured in seconds since the
          Unix epoch.
      role:
        type: ReturnChatEventRole
        docs: >-
          The role of the entity which generated the Chat Event. There are four
          possible values:

          - `USER`: The user, capable of sending user messages and
          interruptions.

          - `AGENT`: The assistant, capable of sending agent messages.

          - `SYSTEM`: The backend server, capable of transmitting errors.

          - `TOOL`: The function calling mechanism.
      type:
        type: ReturnChatEventType
        docs: >-
          Type of Chat Event. There are eleven Chat Event types:

          - `SYSTEM_PROMPT`: The system prompt used to initialize the session.

          - `CHAT_START_MESSAGE`: Marks the beginning of the chat session.

          - `USER_RECORDING_START_MESSAGE`: Marks when the client began
          streaming audio and the start of audio processing.

          - `USER_MESSAGE`: A message sent by the user.

          - `USER_INTERRUPTION`: A user-initiated interruption while the
          assistant is speaking.

          - `AGENT_MESSAGE`: A response generated by the assistant.

          - `FUNCTION_CALL`: A record of a tool invocation by the assistant.

          - `FUNCTION_CALL_RESPONSE`: The result of a previously invoked
          function or tool.

          - `PAUSE_ONSET`: Marks when the client sent a
          `pause_assistant_message` to pause the assistant.

          - `RESUME_ONSET`: Marks when the client sent a
          `resume_assistant_message` to resume the assistant.

          - `CHAT_END_MESSAGE`: Indicates the end of the chat session.
      message_text:
        type: optional<string>
        docs: >-
          The text of the Chat Event. This field contains the message content
          for each event type listed in the `type` field.
      emotion_features:
        type: optional<string>
        docs: >-
          Stringified JSON containing the prosody model inference results.


          EVI uses the prosody model to measure 48 expressions related to speech
          and vocal characteristics. These results contain a detailed emotional
          and tonal analysis of the audio. Scores typically range from 0 to 1,
          with higher values indicating a stronger confidence level in the
          measured attribute.
      metadata:
        type: optional<string>
        docs: Stringified JSON with additional metadata about the chat event.
    source:
      openapi: evi-openapi.json
  ReturnConfigSpec:
    docs: The Config associated with this Chat.
    properties:
      id:
        type: string
        docs: Identifier for a Config. Formatted as a UUID.
      version:
        type: optional<integer>
        docs: >-
          Version number for a Config.


          Configs, Prompts, Custom Voices, and Tools are versioned. This
          versioning system supports iterative development, allowing you to
          progressively refine configurations and revert to previous versions if
          needed.


          Version numbers are integer values representing different iterations
          of the Config. Each update to the Config increments its version
          number.
    source:
      openapi: evi-openapi.json
  ReturnChatGroup:
    docs: A description of chat_group and its status
    properties:
      id:
        type: string
        docs: >-
          Identifier for the Chat Group. Any Chat resumed from this Chat Group
          will have the same `chat_group_id`. Formatted as a UUID.
      first_start_timestamp:
        type: long
        docs: >-
          Time at which the first Chat in this Chat Group was created. Measured
          in seconds since the Unix epoch.
      most_recent_start_timestamp:
        type: long
        docs: >-
          Time at which the most recent Chat in this Chat Group was created.
          Measured in seconds since the Unix epoch.
      most_recent_chat_id:
        type: optional<string>
        docs: >-
          The `chat_id` of the most recent Chat in this Chat Group. Formatted as
          a UUID.
      most_recent_config: optional<ReturnConfigSpec>
      num_chats:
        type: integer
        docs: The total number of Chats in this Chat Group.
      active:
        type: optional<boolean>
        docs: >-
          Denotes whether there is an active Chat associated with this Chat
          Group.
    source:
      openapi: evi-openapi.json
  PostedEventMessageSpec:
    docs: Settings for a specific event_message to be posted to the server
    properties:
      enabled:
        type: boolean
        docs: >-
          Boolean indicating if this event message is enabled.


          If set to `true`, a message will be sent when the circumstances for
          the specific event are met.
      text:
        type: optional<string>
        docs: >-
          Text to use as the event message when the corresponding event occurs.
          If no text is specified, EVI will generate an appropriate message
          based on its current context and the system prompt.
    source:
      openapi: evi-openapi.json
  PostedTimeoutSpec:
    docs: Settings for a specific timeout to be posted to the server
    properties:
      enabled:
        type: boolean
        docs: Boolean indicating if this event message is enabled.
      duration_secs:
        type: optional<integer>
        docs: Duration in seconds for the timeout.
    source:
      openapi: evi-openapi.json
  ReturnEventMessageSpec:
    docs: A specific event message configuration to be returned from the server
    properties:
      enabled:
        type: boolean
        docs: >-
          Boolean indicating if this event message is enabled.


          If set to `true`, a message will be sent when the circumstances for
          the specific event are met.
      text:
        type: optional<string>
        docs: >-
          Text to use as the event message when the corresponding event occurs.
          If no text is specified, EVI will generate an appropriate message
          based on its current context and the system prompt.
    source:
      openapi: evi-openapi.json
  ReturnTimeoutSpec:
    docs: A specific timeout configuration to be returned from the server
    properties:
      enabled:
        type: boolean
        docs: >-
          Boolean indicating if this timeout is enabled.


          If set to false, EVI will not timeout due to a specified duration
          being reached. However, the conversation will eventually disconnect
          after 1,800 seconds (30 minutes), which is the maximum WebSocket
          duration limit for EVI.
      duration_secs:
        type: optional<integer>
        docs: >-
          Duration in seconds for the timeout (e.g. 600 seconds represents 10
          minutes).
    source:
      openapi: evi-openapi.json
  VoiceProvider:
    enum:
      - HUME_AI
      - CUSTOM_VOICE
    source:
      openapi: evi-openapi.json
  VoiceId:
    properties:
      id:
        type: string
        docs: ID of the voice in the `Voice Library`.
      provider:
        type: optional<VoiceProvider>
        docs: Model provider associated with this Voice ID.
    source:
      openapi: evi-openapi.json
  VoiceName:
    properties:
      name:
        type: string
        docs: Name of the voice in the `Voice Library`.
      provider:
        type: optional<VoiceProvider>
        docs: Model provider associated with this Voice Name.
    source:
      openapi: evi-openapi.json
  VoiceRef:
    discriminated: false
    union:
      - type: VoiceId
      - type: VoiceName
    source:
      openapi: evi-openapi.json
