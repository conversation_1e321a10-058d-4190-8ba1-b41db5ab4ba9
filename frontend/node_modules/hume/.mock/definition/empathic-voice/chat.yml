channel:
  path: /chat
  url: prod
  auth: false
  docs: Chat with Empathic Voice Interface (EVI)
  query-parameters:
    access_token:
      type: optional<string>
      default: ''
      docs: >-
        Access token used for authenticating the client. If not provided, an
        `api_key` must be provided to authenticate.


        The access token is generated using both an API key and a Secret key,
        which provides an additional layer of security compared to using just an
        API key.


        For more details, refer to the [Authentication Strategies
        Guide](/docs/introduction/api-key#authentication-strategies).
    config_id:
      type: optional<string>
      docs: >-
        The unique identifier for an EVI configuration.


        Include this ID in your connection request to equip EVI with the Prompt,
        Language Model, Voice, and Tools associated with the specified
        configuration. If omitted, EVI will apply [default configuration
        settings](/docs/empathic-voice-interface-evi/configuration/build-a-configuration#default-configuration).


        For help obtaining this ID, see our [Configuration
        Guide](/docs/empathic-voice-interface-evi/configuration).
    config_version:
      type: optional<integer>
      docs: >-
        The version number of the EVI configuration specified by the
        `config_id`.


        Configs, as well as Prompts and Tools, are versioned. This versioning
        system supports iterative development, allowing you to progressively
        refine configurations and revert to previous versions if needed.


        Include this parameter to apply a specific version of an EVI
        configuration. If omitted, the latest version will be applied.
    event_limit:
      type: optional<integer>
      docs: >-
        The maximum number of chat events to return from chat history. By
        default, the system returns up to 300 events (100 events per page × 3
        pages). Set this parameter to a smaller value to limit the number of
        events returned.
    resumed_chat_group_id:
      type: optional<string>
      docs: >-
        The unique identifier for a Chat Group. Use this field to preserve
        context from a previous Chat session.


        A Chat represents a single session from opening to closing a WebSocket
        connection. In contrast, a Chat Group is a series of resumed Chats that
        collectively represent a single conversation spanning multiple sessions.
        Each Chat includes a Chat Group ID, which is used to preserve the
        context of previous Chat sessions when starting a new one.


        Including the Chat Group ID in the `resumed_chat_group_id` query
        parameter is useful for seamlessly resuming a Chat after unexpected
        network disconnections and for picking up conversations exactly where
        you left off at a later time. This ensures preserved context across
        multiple sessions.


        There are three ways to obtain the Chat Group ID:


        - [Chat
        Metadata](/reference/empathic-voice-interface-evi/chat/chat#receive.ChatMetadata.type):
        Upon establishing a WebSocket connection with EVI, the user receives a
        Chat Metadata message. This message contains a `chat_group_id`, which
        can be used to resume conversations within this chat group in future
        sessions.


        - [List Chats
        endpoint](/reference/empathic-voice-interface-evi/chats/list-chats): Use
        the GET `/v0/evi/chats` endpoint to obtain the Chat Group ID of
        individual Chat sessions. This endpoint lists all available Chat
        sessions and their associated Chat Group ID.


        - [List Chat Groups
        endpoint](/reference/empathic-voice-interface-evi/chat-groups/list-chat-groups):
        Use the GET `/v0/evi/chat_groups` endpoint to obtain the Chat Group IDs
        of all Chat Groups associated with an API key. This endpoint returns a
        list of all available chat groups.
    verbose_transcription:
      type: optional<boolean>
      default: false
      docs: >-
        A flag to enable verbose transcription. Set this query parameter to
        `true` to have unfinalized user transcripts be sent to the client as
        interim UserMessage messages. The
        [interim](/reference/empathic-voice-interface-evi/chat/chat#receive.UserMessage.interim)
        field on a
        [UserMessage](/reference/empathic-voice-interface-evi/chat/chat#receive.UserMessage.type)
        denotes whether the message is "interim" or "final."
    api_key:
      type: optional<string>
      default: ''
      docs: >-
        API key used for authenticating the client. If not provided, an
        `access_token` must be provided to authenticate.


        For more details, refer to the [Authentication Strategies
        Guide](/docs/introduction/api-key#authentication-strategies).
  messages:
    publish:
      origin: client
      body: PublishEvent
    subscribe:
      origin: server
      body: SubscribeEvent
  examples:
    - messages:
        - type: publish
          body:
            data: data
            type: audio_input
        - type: subscribe
          body:
            type: assistant_end
imports:
  root: __package__.yml
types:
  PublishEvent:
    discriminated: false
    union:
      - type: root.AudioInput
      - type: root.SessionSettings
      - type: root.UserInput
      - type: root.AssistantInput
      - type: root.ToolResponseMessage
      - type: root.ToolErrorMessage
      - type: root.PauseAssistantMessage
      - type: root.ResumeAssistantMessage
    source:
      openapi: evi-asyncapi.json
  SubscribeEvent:
    discriminated: false
    union:
      - type: root.AssistantEnd
      - type: root.AssistantMessage
      - type: root.AssistantProsody
      - type: root.AudioOutput
      - type: root.ChatMetadata
      - type: root.WebSocketError
      - type: root.UserInterruption
      - type: root.UserMessage
      - type: root.ToolCallMessage
      - type: root.ToolResponseMessage
      - type: root.ToolErrorMessage
    source:
      openapi: evi-asyncapi.json
