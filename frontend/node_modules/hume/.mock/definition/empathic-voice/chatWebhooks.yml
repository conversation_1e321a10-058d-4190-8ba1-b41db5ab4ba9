imports:
  root: __package__.yml
webhooks:
  chatEnded:
    audiences: []
    method: POST
    display-name: Chat Ended
    headers: {}
    payload: root.WebhookEventChatEnded
    examples:
      - payload:
          chat_group_id: 9fc18597-3567-42d5-94d6-935bde84bf2f
          chat_id: 470a49f6-1dec-4afe-8b61-035d3b2d63b0
          config_id: 1b60e1a0-cc59-424a-8d2c-189d354db3f3
          duration_seconds: 180
          end_reason: USER_ENDED
          end_time: 1716244958546
          event_name: chat_ended
    docs: Sent when an EVI chat ends.
  chatStarted:
    audiences: []
    method: POST
    display-name: Chat Started
    headers: {}
    payload: root.WebhookEventChatStarted
    examples:
      - payload:
          chat_group_id: 9fc18597-3567-42d5-94d6-935bde84bf2f
          chat_id: 470a49f6-1dec-4afe-8b61-035d3b2d63b0
          config_id: 1b60e1a0-cc59-424a-8d2c-189d354db3f3
          chat_start_type: new_chat_group
          event_name: chat_started
          start_time: 1716244940648
    docs: Sent when an EVI chat is started.
