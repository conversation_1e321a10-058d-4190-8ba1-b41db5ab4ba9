imports:
  root: __package__.yml
service:
  auth: false
  base-path: ''
  endpoints:
    list-configs:
      path: /v0/evi/configs
      method: GET
      auth: true
      docs: >-
        Fetches a paginated list of **Configs**.


        For more details on configuration options and how to configure EVI, see
        our [configuration guide](/docs/speech-to-speech-evi/configuration).
      pagination:
        offset: $request.page_number
        results: $response.configs_page
      source:
        openapi: evi-openapi.json
      display-name: List configs
      request:
        name: ConfigsListConfigsRequest
        query-parameters:
          page_number:
            type: optional<integer>
            default: 0
            docs: >-
              Specifies the page number to retrieve, enabling pagination.


              This parameter uses zero-based indexing. For example, setting
              `page_number` to 0 retrieves the first page of results (items 0-9
              if `page_size` is 10), setting `page_number` to 1 retrieves the
              second page (items 10-19), and so on. Defaults to 0, which
              retrieves the first page.
          page_size:
            type: optional<integer>
            docs: >-
              Specifies the maximum number of results to include per page,
              enabling pagination. The value must be between 1 and 100,
              inclusive.


              For example, if `page_size` is set to 10, each page will include
              up to 10 items. Defaults to 10.
          restrict_to_most_recent:
            type: optional<boolean>
            docs: >-
              By default, `restrict_to_most_recent` is set to true, returning
              only the latest version of each tool. To include all versions of
              each tool in the list, set `restrict_to_most_recent` to false.
          name:
            type: optional<string>
            docs: Filter to only include configs with this name.
      response:
        docs: Success
        type: root.ReturnPagedConfigs
        status-code: 200
      errors:
        - root.BadRequestError
      examples:
        - query-parameters:
            page_number: 0
            page_size: 1
          response:
            body:
              page_number: 0
              page_size: 1
              total_pages: 1
              configs_page:
                - id: 1b60e1a0-cc59-424a-8d2c-189d354db3f3
                  version: 0
                  version_description: ''
                  name: Weather Assistant Config
                  created_on: 1715267200693
                  modified_on: 1715267200693
                  evi_version: '3'
                  prompt:
                    id: af699d45-2985-42cc-91b9-af9e5da3bac5
                    version: 0
                    version_type: FIXED
                    version_description: ''
                    name: Weather Assistant Prompt
                    created_on: 1715267200693
                    modified_on: 1715267200693
                    text: >-
                      <role>You are an AI weather assistant providing users with
                      accurate and up-to-date weather information. Respond to
                      user queries concisely and clearly. Use simple language
                      and avoid technical jargon. Provide temperature,
                      precipitation, wind conditions, and any weather alerts.
                      Include helpful tips if severe weather is expected.</role>
                  voice:
                    provider: HUME_AI
                    name: Ava Song
                    id: 5bb7de05-c8fe-426a-8fcc-ba4fc4ce9f9c
                  language_model:
                    model_provider: ANTHROPIC
                    model_resource: claude-3-7-sonnet-latest
                    temperature: 1
                  ellm_model:
                    allow_short_responses: false
                  tools: []
                  builtin_tools: []
                  event_messages:
                    on_new_chat:
                      enabled: false
                      text: ''
                    on_inactivity_timeout:
                      enabled: false
                      text: ''
                    on_max_duration_timeout:
                      enabled: false
                      text: ''
                  timeouts:
                    inactivity:
                      enabled: true
                      duration_secs: 600
                    max_duration:
                      enabled: true
                      duration_secs: 1800
    create-config:
      path: /v0/evi/configs
      method: POST
      auth: true
      docs: >-
        Creates a **Config** which can be applied to EVI.


        For more details on configuration options and how to configure EVI, see
        our [configuration guide](/docs/speech-to-speech-evi/configuration).
      source:
        openapi: evi-openapi.json
      display-name: Create config
      request:
        name: PostedConfig
        body:
          properties:
            evi_version:
              type: string
              docs: >-
                Specifies the EVI version to use. See our [EVI Version 
                Guide](/docs/speech-to-speech-evi/configuration/evi-version) for
                differences between versions.


                **We're officially sunsetting EVI versions 1 and 2 on August 30,
                2025**. To keep things running smoothly, be sure to [migrate to
                EVI
                3](/docs/speech-to-speech-evi/configuration/evi-version#migrating-to-evi-3)
                before then.
            name:
              type: string
              docs: Name applied to all versions of a particular Config.
            version_description:
              type: optional<string>
              docs: An optional description of the Config version.
            prompt: optional<root.PostedConfigPromptSpec>
            voice:
              type: optional<root.VoiceRef>
              docs: A voice specification associated with this Config.
            language_model:
              type: optional<root.PostedLanguageModel>
              docs: >-
                The supplemental language model associated with this Config.


                This model is used to generate longer, more detailed responses
                from EVI. Choosing an appropriate supplemental language model
                for your use case is crucial for generating fast, high-quality
                responses from EVI.
            ellm_model:
              type: optional<root.PostedEllmModel>
              docs: >-
                The eLLM setup associated with this Config.


                Hume's eLLM (empathic Large Language Model) is a multimodal
                language model that takes into account both expression measures
                and language. The eLLM generates short, empathic language
                responses and guides text-to-speech (TTS) prosody.
            tools:
              type: optional<list<optional<root.PostedUserDefinedToolSpec>>>
              docs: List of user-defined tools associated with this Config.
            builtin_tools:
              type: optional<list<optional<root.PostedBuiltinTool>>>
              docs: List of built-in tools associated with this Config.
            event_messages: optional<root.PostedEventMessageSpecs>
            nudges:
              type: optional<root.PostedNudgeSpec>
              docs: >-
                Configures nudges, brief audio prompts that can guide
                conversations when users pause or need encouragement to continue
                speaking. Nudges help create more natural, flowing interactions
                by providing gentle conversational cues. 
            timeouts: optional<root.PostedTimeoutSpecs>
            webhooks:
              type: optional<list<optional<root.PostedWebhookSpec>>>
              docs: Webhook config specifications for each subscriber.
        content-type: application/json
      response:
        docs: Created
        type: root.ReturnConfig
        status-code: 201
      errors:
        - root.BadRequestError
      examples:
        - request:
            name: Weather Assistant Config
            prompt:
              id: af699d45-2985-42cc-91b9-af9e5da3bac5
              version: 0
            evi_version: '3'
            voice:
              provider: HUME_AI
              name: Ava Song
            language_model:
              model_provider: ANTHROPIC
              model_resource: claude-3-7-sonnet-latest
              temperature: 1
            event_messages:
              on_new_chat:
                enabled: false
                text: ''
              on_inactivity_timeout:
                enabled: false
                text: ''
              on_max_duration_timeout:
                enabled: false
                text: ''
          response:
            body:
              id: 1b60e1a0-cc59-424a-8d2c-189d354db3f3
              version: 0
              version_description: ''
              name: Weather Assistant Config
              created_on: 1715275452390
              modified_on: 1715275452390
              evi_version: '3'
              prompt:
                id: af699d45-2985-42cc-91b9-af9e5da3bac5
                version: 0
                version_type: FIXED
                version_description: ''
                name: Weather Assistant Prompt
                created_on: 1715267200693
                modified_on: 1715267200693
                text: >-
                  <role>You are an AI weather assistant providing users with
                  accurate and up-to-date weather information. Respond to user
                  queries concisely and clearly. Use simple language and avoid
                  technical jargon. Provide temperature, precipitation, wind
                  conditions, and any weather alerts. Include helpful tips if
                  severe weather is expected.</role>
              voice:
                provider: HUME_AI
                name: Ava Song
                id: 5bb7de05-c8fe-426a-8fcc-ba4fc4ce9f9c
              language_model:
                model_provider: ANTHROPIC
                model_resource: claude-3-7-sonnet-latest
                temperature: 1
              ellm_model:
                allow_short_responses: false
              tools: []
              builtin_tools: []
              event_messages:
                on_new_chat:
                  enabled: false
                  text: ''
                on_inactivity_timeout:
                  enabled: false
                  text: ''
                on_max_duration_timeout:
                  enabled: false
                  text: ''
              timeouts:
                inactivity:
                  enabled: true
                  duration_secs: 600
                max_duration:
                  enabled: true
                  duration_secs: 1800
    list-config-versions:
      path: /v0/evi/configs/{id}
      method: GET
      auth: true
      docs: >-
        Fetches a list of a **Config's** versions.


        For more details on configuration options and how to configure EVI, see
        our [configuration guide](/docs/speech-to-speech-evi/configuration).
      pagination:
        offset: $request.page_number
        results: $response.configs_page
      source:
        openapi: evi-openapi.json
      path-parameters:
        id:
          type: string
          docs: Identifier for a Config. Formatted as a UUID.
      display-name: List config versions
      request:
        name: ConfigsListConfigVersionsRequest
        query-parameters:
          page_number:
            type: optional<integer>
            default: 0
            docs: >-
              Specifies the page number to retrieve, enabling pagination.


              This parameter uses zero-based indexing. For example, setting
              `page_number` to 0 retrieves the first page of results (items 0-9
              if `page_size` is 10), setting `page_number` to 1 retrieves the
              second page (items 10-19), and so on. Defaults to 0, which
              retrieves the first page.
          page_size:
            type: optional<integer>
            docs: >-
              Specifies the maximum number of results to include per page,
              enabling pagination. The value must be between 1 and 100,
              inclusive.


              For example, if `page_size` is set to 10, each page will include
              up to 10 items. Defaults to 10.
          restrict_to_most_recent:
            type: optional<boolean>
            docs: >-
              By default, `restrict_to_most_recent` is set to true, returning
              only the latest version of each config. To include all versions of
              each config in the list, set `restrict_to_most_recent` to false.
      response:
        docs: Success
        type: root.ReturnPagedConfigs
        status-code: 200
      errors:
        - root.BadRequestError
      examples:
        - path-parameters:
            id: 1b60e1a0-cc59-424a-8d2c-189d354db3f3
          response:
            body:
              page_number: 0
              page_size: 10
              total_pages: 1
              configs_page:
                - id: 1b60e1a0-cc59-424a-8d2c-189d354db3f3
                  version: 0
                  version_description: ''
                  name: Weather Assistant Config
                  created_on: 1715275452390
                  modified_on: 1715275452390
                  evi_version: '3'
                  prompt:
                    id: af699d45-2985-42cc-91b9-af9e5da3bac5
                    version: 0
                    version_type: FIXED
                    version_description: ''
                    name: Weather Assistant Prompt
                    created_on: 1715267200693
                    modified_on: 1715267200693
                    text: >-
                      <role>You are an AI weather assistant providing users with
                      accurate and up-to-date weather information. Respond to
                      user queries concisely and clearly. Use simple language
                      and avoid technical jargon. Provide temperature,
                      precipitation, wind conditions, and any weather alerts.
                      Include helpful tips if severe weather is expected.</role>
                  voice:
                    provider: HUME_AI
                    name: Ava Song
                    id: 5bb7de05-c8fe-426a-8fcc-ba4fc4ce9f9c
                  language_model:
                    model_provider: ANTHROPIC
                    model_resource: claude-3-7-sonnet-latest
                    temperature: 1
                  ellm_model:
                    allow_short_responses: false
                  tools: []
                  builtin_tools: []
                  event_messages:
                    on_new_chat:
                      enabled: false
                      text: ''
                    on_inactivity_timeout:
                      enabled: false
                      text: ''
                    on_max_duration_timeout:
                      enabled: false
                      text: ''
                  timeouts:
                    inactivity:
                      enabled: true
                      duration_secs: 600
                    max_duration:
                      enabled: true
                      duration_secs: 1800
    create-config-version:
      path: /v0/evi/configs/{id}
      method: POST
      auth: true
      docs: >-
        Updates a **Config** by creating a new version of the **Config**.


        For more details on configuration options and how to configure EVI, see
        our [configuration guide](/docs/speech-to-speech-evi/configuration).
      source:
        openapi: evi-openapi.json
      path-parameters:
        id:
          type: string
          docs: Identifier for a Config. Formatted as a UUID.
      display-name: Create config version
      request:
        name: PostedConfigVersion
        body:
          properties:
            evi_version:
              type: string
              docs: The version of the EVI used with this config.
            version_description:
              type: optional<string>
              docs: An optional description of the Config version.
            prompt: optional<root.PostedConfigPromptSpec>
            voice:
              type: optional<root.VoiceRef>
              docs: A voice specification associated with this Config version.
            language_model:
              type: optional<root.PostedLanguageModel>
              docs: >-
                The supplemental language model associated with this Config
                version.


                This model is used to generate longer, more detailed responses
                from EVI. Choosing an appropriate supplemental language model
                for your use case is crucial for generating fast, high-quality
                responses from EVI.
            ellm_model:
              type: optional<root.PostedEllmModel>
              docs: >-
                The eLLM setup associated with this Config version.


                Hume's eLLM (empathic Large Language Model) is a multimodal
                language model that takes into account both expression measures
                and language. The eLLM generates short, empathic language
                responses and guides text-to-speech (TTS) prosody.
            tools:
              type: optional<list<optional<root.PostedUserDefinedToolSpec>>>
              docs: List of user-defined tools associated with this Config version.
            builtin_tools:
              type: optional<list<optional<root.PostedBuiltinTool>>>
              docs: List of built-in tools associated with this Config version.
            event_messages: optional<root.PostedEventMessageSpecs>
            timeouts: optional<root.PostedTimeoutSpecs>
            nudges: optional<root.PostedNudgeSpec>
            webhooks:
              type: optional<list<optional<root.PostedWebhookSpec>>>
              docs: Webhook config specifications for each subscriber.
        content-type: application/json
      response:
        docs: Created
        type: root.ReturnConfig
        status-code: 201
      errors:
        - root.BadRequestError
      examples:
        - path-parameters:
            id: 1b60e1a0-cc59-424a-8d2c-189d354db3f3
          request:
            version_description: This is an updated version of the Weather Assistant Config.
            evi_version: '3'
            prompt:
              id: af699d45-2985-42cc-91b9-af9e5da3bac5
              version: 0
            voice:
              provider: HUME_AI
              name: Ava Song
            language_model:
              model_provider: ANTHROPIC
              model_resource: claude-3-7-sonnet-latest
              temperature: 1
            ellm_model:
              allow_short_responses: true
            event_messages:
              on_new_chat:
                enabled: false
                text: ''
              on_inactivity_timeout:
                enabled: false
                text: ''
              on_max_duration_timeout:
                enabled: false
                text: ''
          response:
            body:
              id: 1b60e1a0-cc59-424a-8d2c-189d354db3f3
              version: 1
              version_description: This is an updated version of the Weather Assistant Config.
              name: Weather Assistant Config
              created_on: 1715275452390
              modified_on: 1722642242998
              evi_version: '3'
              prompt:
                id: af699d45-2985-42cc-91b9-af9e5da3bac5
                version: 0
                version_type: FIXED
                version_description: ''
                name: Weather Assistant Prompt
                created_on: 1715267200693
                modified_on: 1715267200693
                text: >-
                  <role>You are an AI weather assistant providing users with
                  accurate and up-to-date weather information. Respond to user
                  queries concisely and clearly. Use simple language and avoid
                  technical jargon. Provide temperature, precipitation, wind
                  conditions, and any weather alerts. Include helpful tips if
                  severe weather is expected.</role>
              voice:
                provider: HUME_AI
                name: Ava Song
                id: 5bb7de05-c8fe-426a-8fcc-ba4fc4ce9f9c
              language_model:
                model_provider: ANTHROPIC
                model_resource: claude-3-7-sonnet-latest
                temperature: 1
              ellm_model:
                allow_short_responses: true
              tools: []
              builtin_tools: []
              event_messages:
                on_new_chat:
                  enabled: false
                  text: ''
                on_inactivity_timeout:
                  enabled: false
                  text: ''
                on_max_duration_timeout:
                  enabled: false
                  text: ''
              timeouts:
                inactivity:
                  enabled: true
                  duration_secs: 600
                max_duration:
                  enabled: true
                  duration_secs: 1800
    delete-config:
      path: /v0/evi/configs/{id}
      method: DELETE
      auth: true
      docs: >-
        Deletes a **Config** and its versions.


        For more details on configuration options and how to configure EVI, see
        our [configuration guide](/docs/speech-to-speech-evi/configuration).
      source:
        openapi: evi-openapi.json
      path-parameters:
        id:
          type: string
          docs: Identifier for a Config. Formatted as a UUID.
      display-name: Delete config
      errors:
        - root.BadRequestError
      examples:
        - path-parameters:
            id: 1b60e1a0-cc59-424a-8d2c-189d354db3f3
    update-config-name:
      path: /v0/evi/configs/{id}
      method: PATCH
      auth: true
      docs: >-
        Updates the name of a **Config**.


        For more details on configuration options and how to configure EVI, see
        our [configuration guide](/docs/speech-to-speech-evi/configuration).
      source:
        openapi: evi-openapi.json
      path-parameters:
        id:
          type: string
          docs: Identifier for a Config. Formatted as a UUID.
      display-name: Update config name
      request:
        name: PostedConfigName
        body:
          properties:
            name:
              type: string
              docs: Name applied to all versions of a particular Config.
        content-type: application/json
      response:
        docs: Success
        type: text
        status-code: 200
      errors:
        - root.BadRequestError
      examples:
        - path-parameters:
            id: 1b60e1a0-cc59-424a-8d2c-189d354db3f3
          request:
            name: Updated Weather Assistant Config Name
    get-config-version:
      path: /v0/evi/configs/{id}/version/{version}
      method: GET
      auth: true
      docs: >-
        Fetches a specified version of a **Config**.


        For more details on configuration options and how to configure EVI, see
        our [configuration guide](/docs/speech-to-speech-evi/configuration).
      source:
        openapi: evi-openapi.json
      path-parameters:
        id:
          type: string
          docs: Identifier for a Config. Formatted as a UUID.
        version:
          type: integer
          docs: >-
            Version number for a Config.


            Configs, Prompts, Custom Voices, and Tools are versioned. This
            versioning system supports iterative development, allowing you to
            progressively refine configurations and revert to previous versions
            if needed.


            Version numbers are integer values representing different iterations
            of the Config. Each update to the Config increments its version
            number.
      display-name: Get config version
      response:
        docs: Success
        type: root.ReturnConfig
        status-code: 200
      errors:
        - root.BadRequestError
      examples:
        - path-parameters:
            id: 1b60e1a0-cc59-424a-8d2c-189d354db3f3
            version: 1
          response:
            body:
              id: 1b60e1a0-cc59-424a-8d2c-189d354db3f3
              version: 1
              version_description: ''
              name: Weather Assistant Config
              created_on: 1715275452390
              modified_on: 1715275452390
              evi_version: '3'
              prompt:
                id: af699d45-2985-42cc-91b9-af9e5da3bac5
                version: 0
                version_type: FIXED
                version_description: ''
                name: Weather Assistant Prompt
                created_on: 1715267200693
                modified_on: 1715267200693
                text: >-
                  <role>You are an AI weather assistant providing users with
                  accurate and up-to-date weather information. Respond to user
                  queries concisely and clearly. Use simple language and avoid
                  technical jargon. Provide temperature, precipitation, wind
                  conditions, and any weather alerts. Include helpful tips if
                  severe weather is expected.</role>
              voice:
                provider: HUME_AI
                name: Ava Song
                id: 5bb7de05-c8fe-426a-8fcc-ba4fc4ce9f9c
              language_model:
                model_provider: ANTHROPIC
                model_resource: claude-3-7-sonnet-latest
                temperature: 1
              ellm_model:
                allow_short_responses: false
              tools: []
              builtin_tools: []
              event_messages:
                on_new_chat:
                  enabled: false
                  text: ''
                on_inactivity_timeout:
                  enabled: false
                  text: ''
                on_max_duration_timeout:
                  enabled: false
                  text: ''
              timeouts:
                inactivity:
                  enabled: true
                  duration_secs: 600
                max_duration:
                  enabled: true
                  duration_secs: 1800
    delete-config-version:
      path: /v0/evi/configs/{id}/version/{version}
      method: DELETE
      auth: true
      docs: >-
        Deletes a specified version of a **Config**.


        For more details on configuration options and how to configure EVI, see
        our [configuration guide](/docs/speech-to-speech-evi/configuration).
      source:
        openapi: evi-openapi.json
      path-parameters:
        id:
          type: string
          docs: Identifier for a Config. Formatted as a UUID.
        version:
          type: integer
          docs: >-
            Version number for a Config.


            Configs, Prompts, Custom Voices, and Tools are versioned. This
            versioning system supports iterative development, allowing you to
            progressively refine configurations and revert to previous versions
            if needed.


            Version numbers are integer values representing different iterations
            of the Config. Each update to the Config increments its version
            number.
      display-name: Delete config version
      errors:
        - root.BadRequestError
      examples:
        - path-parameters:
            id: 1b60e1a0-cc59-424a-8d2c-189d354db3f3
            version: 1
    update-config-description:
      path: /v0/evi/configs/{id}/version/{version}
      method: PATCH
      auth: true
      docs: >-
        Updates the description of a **Config**.


        For more details on configuration options and how to configure EVI, see
        our [configuration guide](/docs/speech-to-speech-evi/configuration).
      source:
        openapi: evi-openapi.json
      path-parameters:
        id:
          type: string
          docs: Identifier for a Config. Formatted as a UUID.
        version:
          type: integer
          docs: >-
            Version number for a Config.


            Configs, Prompts, Custom Voices, and Tools are versioned. This
            versioning system supports iterative development, allowing you to
            progressively refine configurations and revert to previous versions
            if needed.


            Version numbers are integer values representing different iterations
            of the Config. Each update to the Config increments its version
            number.
      display-name: Update config description
      request:
        name: PostedConfigVersionDescription
        body:
          properties:
            version_description:
              type: optional<string>
              docs: An optional description of the Config version.
        content-type: application/json
      response:
        docs: Success
        type: root.ReturnConfig
        status-code: 200
      errors:
        - root.BadRequestError
      examples:
        - path-parameters:
            id: 1b60e1a0-cc59-424a-8d2c-189d354db3f3
            version: 1
          request:
            version_description: This is an updated version_description.
          response:
            body:
              id: 1b60e1a0-cc59-424a-8d2c-189d354db3f3
              version: 1
              version_description: This is an updated version_description.
              name: Weather Assistant Config
              created_on: 1715275452390
              modified_on: 1715275452390
              evi_version: '3'
              prompt:
                id: af699d45-2985-42cc-91b9-af9e5da3bac5
                version: 0
                version_type: FIXED
                version_description: ''
                name: Weather Assistant Prompt
                created_on: 1715267200693
                modified_on: 1715267200693
                text: >-
                  <role>You are an AI weather assistant providing users with
                  accurate and up-to-date weather information. Respond to user
                  queries concisely and clearly. Use simple language and avoid
                  technical jargon. Provide temperature, precipitation, wind
                  conditions, and any weather alerts. Include helpful tips if
                  severe weather is expected.</role>
              voice:
                provider: HUME_AI
                name: Ava Song
                id: 5bb7de05-c8fe-426a-8fcc-ba4fc4ce9f9c
              language_model:
                model_provider: ANTHROPIC
                model_resource: claude-3-7-sonnet-latest
                temperature: 1
              ellm_model:
                allow_short_responses: false
              tools: []
              builtin_tools: []
              event_messages:
                on_new_chat:
                  enabled: false
                  text: ''
                on_inactivity_timeout:
                  enabled: false
                  text: ''
                on_max_duration_timeout:
                  enabled: false
                  text: ''
              timeouts:
                inactivity:
                  enabled: true
                  duration_secs: 600
                max_duration:
                  enabled: true
                  duration_secs: 1800
  source:
    openapi: evi-openapi.json
