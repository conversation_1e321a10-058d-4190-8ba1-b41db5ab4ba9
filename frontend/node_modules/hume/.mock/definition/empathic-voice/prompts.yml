imports:
  root: __package__.yml
service:
  auth: false
  base-path: ''
  endpoints:
    list-prompts:
      path: /v0/evi/prompts
      method: GET
      auth: true
      docs: >-
        Fetches a paginated list of **Prompts**.


        See our [prompting
        guide](/docs/speech-to-speech-evi/guides/phone-calling) for tips on
        crafting your system prompt.
      pagination:
        offset: $request.page_number
        results: $response.prompts_page
      source:
        openapi: evi-openapi.json
      display-name: List prompts
      request:
        name: PromptsListPromptsRequest
        query-parameters:
          page_number:
            type: optional<integer>
            default: 0
            docs: >-
              Specifies the page number to retrieve, enabling pagination.


              This parameter uses zero-based indexing. For example, setting
              `page_number` to 0 retrieves the first page of results (items 0-9
              if `page_size` is 10), setting `page_number` to 1 retrieves the
              second page (items 10-19), and so on. Defaults to 0, which
              retrieves the first page.
          page_size:
            type: optional<integer>
            docs: >-
              Specifies the maximum number of results to include per page,
              enabling pagination. The value must be between 1 and 100,
              inclusive.


              For example, if `page_size` is set to 10, each page will include
              up to 10 items. Defaults to 10.
          restrict_to_most_recent:
            type: optional<boolean>
            docs: Only include the most recent version of each prompt in the list.
          name:
            type: optional<string>
            docs: Filter to only include prompts with name.
      response:
        docs: Success
        type: root.ReturnPagedPrompts
        status-code: 200
      errors:
        - root.BadRequestError
      examples:
        - query-parameters:
            page_number: 0
            page_size: 2
          response:
            body:
              page_number: 0
              page_size: 2
              total_pages: 1
              prompts_page:
                - id: af699d45-2985-42cc-91b9-af9e5da3bac5
                  version: 0
                  version_type: FIXED
                  version_description: ''
                  name: Weather Assistant Prompt
                  created_on: 1715267200693
                  modified_on: 1715267200693
                  text: >-
                    <role>You are an AI weather assistant providing users with
                    accurate and up-to-date weather information. Respond to user
                    queries concisely and clearly. Use simple language and avoid
                    technical jargon. Provide temperature, precipitation, wind
                    conditions, and any weather alerts. Include helpful tips if
                    severe weather is expected.</role>
                - id: 616b2b4c-a096-4445-9c23-64058b564fc2
                  version: 0
                  version_type: FIXED
                  version_description: ''
                  name: Web Search Assistant Prompt
                  created_on: 1715267200693
                  modified_on: 1715267200693
                  text: >-
                    <role>You are an AI web search assistant designed to help
                    users find accurate and relevant information on the web.
                    Respond to user queries promptly, using the built-in web
                    search tool to retrieve up-to-date results. Present
                    information clearly and concisely, summarizing key points
                    where necessary. Use simple language and avoid technical
                    jargon. If needed, provide helpful tips for refining search
                    queries to obtain better results.</role>
    create-prompt:
      path: /v0/evi/prompts
      method: POST
      auth: true
      docs: >-
        Creates a **Prompt** that can be added to an [EVI
        configuration](/reference/speech-to-speech-evi/configs/create-config).


        See our [prompting
        guide](/docs/speech-to-speech-evi/guides/phone-calling) for tips on
        crafting your system prompt.
      source:
        openapi: evi-openapi.json
      display-name: Create prompt
      request:
        name: PostedPrompt
        body:
          properties:
            name:
              type: string
              docs: Name applied to all versions of a particular Prompt.
            version_description:
              type: optional<string>
              docs: An optional description of the Prompt version.
            text:
              type: string
              docs: >-
                Instructions used to shape EVI’s behavior, responses, and style.


                You can use the Prompt to define a specific goal or role for
                EVI, specifying how it should act or what it should focus on
                during the conversation. For example, EVI can be instructed to
                act as a customer support representative, a fitness coach, or a
                travel advisor, each with its own set of behaviors and response
                styles.


                For help writing a system prompt, see our [Prompting
                Guide](/docs/speech-to-speech-evi/guides/prompting).
        content-type: application/json
      response:
        docs: Created
        type: optional<root.ReturnPrompt>
        status-code: 201
      errors:
        - root.BadRequestError
      examples:
        - request:
            name: Weather Assistant Prompt
            text: >-
              <role>You are an AI weather assistant providing users with
              accurate and up-to-date weather information. Respond to user
              queries concisely and clearly. Use simple language and avoid
              technical jargon. Provide temperature, precipitation, wind
              conditions, and any weather alerts. Include helpful tips if severe
              weather is expected.</role>
          response:
            body:
              id: af699d45-2985-42cc-91b9-af9e5da3bac5
              version: 0
              version_type: FIXED
              version_description: null
              name: Weather Assistant Prompt
              created_on: 1722633247488
              modified_on: 1722633247488
              text: >-
                <role>You are an AI weather assistant providing users with
                accurate and up-to-date weather information. Respond to user
                queries concisely and clearly. Use simple language and avoid
                technical jargon. Provide temperature, precipitation, wind
                conditions, and any weather alerts. Include helpful tips if
                severe weather is expected.</role>
    list-prompt-versions:
      path: /v0/evi/prompts/{id}
      method: GET
      auth: true
      docs: >-
        Fetches a list of a **Prompt's** versions.


        See our [prompting
        guide](/docs/speech-to-speech-evi/guides/phone-calling) for tips on
        crafting your system prompt.
      source:
        openapi: evi-openapi.json
      path-parameters:
        id:
          type: string
          docs: Identifier for a Prompt. Formatted as a UUID.
      display-name: List prompt versions
      request:
        name: PromptsListPromptVersionsRequest
        query-parameters:
          page_number:
            type: optional<integer>
            default: 0
            docs: >-
              Specifies the page number to retrieve, enabling pagination.


              This parameter uses zero-based indexing. For example, setting
              `page_number` to 0 retrieves the first page of results (items 0-9
              if `page_size` is 10), setting `page_number` to 1 retrieves the
              second page (items 10-19), and so on. Defaults to 0, which
              retrieves the first page.
          page_size:
            type: optional<integer>
            docs: >-
              Specifies the maximum number of results to include per page,
              enabling pagination. The value must be between 1 and 100,
              inclusive.


              For example, if `page_size` is set to 10, each page will include
              up to 10 items. Defaults to 10.
          restrict_to_most_recent:
            type: optional<boolean>
            docs: >-
              By default, `restrict_to_most_recent` is set to true, returning
              only the latest version of each prompt. To include all versions of
              each prompt in the list, set `restrict_to_most_recent` to false.
      response:
        docs: Success
        type: root.ReturnPagedPrompts
        status-code: 200
      errors:
        - root.BadRequestError
      examples:
        - path-parameters:
            id: af699d45-2985-42cc-91b9-af9e5da3bac5
          response:
            body:
              page_number: 0
              page_size: 10
              total_pages: 1
              prompts_page:
                - id: af699d45-2985-42cc-91b9-af9e5da3bac5
                  version: 0
                  version_type: FIXED
                  version_description: ''
                  name: Weather Assistant Prompt
                  created_on: 1722633247488
                  modified_on: 1722633247488
                  text: >-
                    <role>You are an AI weather assistant providing users with
                    accurate and up-to-date weather information. Respond to user
                    queries concisely and clearly. Use simple language and avoid
                    technical jargon. Provide temperature, precipitation, wind
                    conditions, and any weather alerts. Include helpful tips if
                    severe weather is expected.</role>
    create-prompt-version:
      path: /v0/evi/prompts/{id}
      method: POST
      auth: true
      docs: >-
        Updates a **Prompt** by creating a new version of the **Prompt**.


        See our [prompting
        guide](/docs/speech-to-speech-evi/guides/phone-calling) for tips on
        crafting your system prompt.
      source:
        openapi: evi-openapi.json
      path-parameters:
        id:
          type: string
          docs: Identifier for a Prompt. Formatted as a UUID.
      display-name: Create prompt version
      request:
        name: PostedPromptVersion
        body:
          properties:
            version_description:
              type: optional<string>
              docs: An optional description of the Prompt version.
            text:
              type: string
              docs: >-
                Instructions used to shape EVI’s behavior, responses, and style
                for this version of the Prompt.


                You can use the Prompt to define a specific goal or role for
                EVI, specifying how it should act or what it should focus on
                during the conversation. For example, EVI can be instructed to
                act as a customer support representative, a fitness coach, or a
                travel advisor, each with its own set of behaviors and response
                styles.


                For help writing a system prompt, see our [Prompting
                Guide](/docs/speech-to-speech-evi/guides/prompting).
        content-type: application/json
      response:
        docs: Created
        type: optional<root.ReturnPrompt>
        status-code: 201
      errors:
        - root.BadRequestError
      examples:
        - path-parameters:
            id: af699d45-2985-42cc-91b9-af9e5da3bac5
          request:
            text: >-
              <role>You are an updated version of an AI weather assistant
              providing users with accurate and up-to-date weather information.
              Respond to user queries concisely and clearly. Use simple language
              and avoid technical jargon. Provide temperature, precipitation,
              wind conditions, and any weather alerts. Include helpful tips if
              severe weather is expected.</role>
            version_description: This is an updated version of the Weather Assistant Prompt.
          response:
            body:
              id: af699d45-2985-42cc-91b9-af9e5da3bac5
              version: 1
              version_type: FIXED
              version_description: This is an updated version of the Weather Assistant Prompt.
              name: Weather Assistant Prompt
              created_on: 1722633247488
              modified_on: 1722635140150
              text: >-
                <role>You are an updated version of an AI weather assistant
                providing users with accurate and up-to-date weather
                information. Respond to user queries concisely and clearly. Use
                simple language and avoid technical jargon. Provide temperature,
                precipitation, wind conditions, and any weather alerts. Include
                helpful tips if severe weather is expected.</role>
    delete-prompt:
      path: /v0/evi/prompts/{id}
      method: DELETE
      auth: true
      docs: >-
        Deletes a **Prompt** and its versions.


        See our [prompting
        guide](/docs/speech-to-speech-evi/guides/phone-calling) for tips on
        crafting your system prompt.
      source:
        openapi: evi-openapi.json
      path-parameters:
        id:
          type: string
          docs: Identifier for a Prompt. Formatted as a UUID.
      display-name: Delete prompt
      errors:
        - root.BadRequestError
      examples:
        - path-parameters:
            id: af699d45-2985-42cc-91b9-af9e5da3bac5
    update-prompt-name:
      path: /v0/evi/prompts/{id}
      method: PATCH
      auth: true
      docs: >-
        Updates the name of a **Prompt**.


        See our [prompting
        guide](/docs/speech-to-speech-evi/guides/phone-calling) for tips on
        crafting your system prompt.
      source:
        openapi: evi-openapi.json
      path-parameters:
        id:
          type: string
          docs: Identifier for a Prompt. Formatted as a UUID.
      display-name: Update prompt name
      request:
        name: PostedPromptName
        body:
          properties:
            name:
              type: string
              docs: Name applied to all versions of a particular Prompt.
        content-type: application/json
      response:
        docs: Success
        type: text
        status-code: 200
      errors:
        - root.BadRequestError
      examples:
        - path-parameters:
            id: af699d45-2985-42cc-91b9-af9e5da3bac5
          request:
            name: Updated Weather Assistant Prompt Name
    get-prompt-version:
      path: /v0/evi/prompts/{id}/version/{version}
      method: GET
      auth: true
      docs: >-
        Fetches a specified version of a **Prompt**.


        See our [prompting
        guide](/docs/speech-to-speech-evi/guides/phone-calling) for tips on
        crafting your system prompt.
      source:
        openapi: evi-openapi.json
      path-parameters:
        id:
          type: string
          docs: Identifier for a Prompt. Formatted as a UUID.
        version:
          type: integer
          docs: >-
            Version number for a Prompt.


            Prompts, Configs, Custom Voices, and Tools are versioned. This
            versioning system supports iterative development, allowing you to
            progressively refine prompts and revert to previous versions if
            needed.


            Version numbers are integer values representing different iterations
            of the Prompt. Each update to the Prompt increments its version
            number.
      display-name: Get prompt version
      response:
        docs: Success
        type: optional<root.ReturnPrompt>
        status-code: 200
      errors:
        - root.BadRequestError
      examples:
        - path-parameters:
            id: af699d45-2985-42cc-91b9-af9e5da3bac5
            version: 0
          response:
            body:
              id: af699d45-2985-42cc-91b9-af9e5da3bac5
              version: 0
              version_type: FIXED
              version_description: ''
              name: Weather Assistant Prompt
              created_on: 1722633247488
              modified_on: 1722633247488
              text: >-
                <role>You are an AI weather assistant providing users with
                accurate and up-to-date weather information. Respond to user
                queries concisely and clearly. Use simple language and avoid
                technical jargon. Provide temperature, precipitation, wind
                conditions, and any weather alerts. Include helpful tips if
                severe weather is expected.</role>
    delete-prompt-version:
      path: /v0/evi/prompts/{id}/version/{version}
      method: DELETE
      auth: true
      docs: >-
        Deletes a specified version of a **Prompt**.


        See our [prompting
        guide](/docs/speech-to-speech-evi/guides/phone-calling) for tips on
        crafting your system prompt.
      source:
        openapi: evi-openapi.json
      path-parameters:
        id:
          type: string
          docs: Identifier for a Prompt. Formatted as a UUID.
        version:
          type: integer
          docs: >-
            Version number for a Prompt.


            Prompts, Configs, Custom Voices, and Tools are versioned. This
            versioning system supports iterative development, allowing you to
            progressively refine prompts and revert to previous versions if
            needed.


            Version numbers are integer values representing different iterations
            of the Prompt. Each update to the Prompt increments its version
            number.
      display-name: Delete prompt version
      errors:
        - root.BadRequestError
      examples:
        - path-parameters:
            id: af699d45-2985-42cc-91b9-af9e5da3bac5
            version: 1
    update-prompt-description:
      path: /v0/evi/prompts/{id}/version/{version}
      method: PATCH
      auth: true
      docs: >-
        Updates the description of a **Prompt**.


        See our [prompting
        guide](/docs/speech-to-speech-evi/guides/phone-calling) for tips on
        crafting your system prompt.
      source:
        openapi: evi-openapi.json
      path-parameters:
        id:
          type: string
          docs: Identifier for a Prompt. Formatted as a UUID.
        version:
          type: integer
          docs: >-
            Version number for a Prompt.


            Prompts, Configs, Custom Voices, and Tools are versioned. This
            versioning system supports iterative development, allowing you to
            progressively refine prompts and revert to previous versions if
            needed.


            Version numbers are integer values representing different iterations
            of the Prompt. Each update to the Prompt increments its version
            number.
      display-name: Update prompt description
      request:
        name: PostedPromptVersionDescription
        body:
          properties:
            version_description:
              type: optional<string>
              docs: An optional description of the Prompt version.
        content-type: application/json
      response:
        docs: Success
        type: optional<root.ReturnPrompt>
        status-code: 200
      errors:
        - root.BadRequestError
      examples:
        - path-parameters:
            id: af699d45-2985-42cc-91b9-af9e5da3bac5
            version: 1
          request:
            version_description: This is an updated version_description.
          response:
            body:
              id: af699d45-2985-42cc-91b9-af9e5da3bac5
              version: 1
              version_type: FIXED
              version_description: This is an updated version_description.
              name: string
              created_on: 1722633247488
              modified_on: 1722634770585
              text: >-
                <role>You are an AI weather assistant providing users with
                accurate and up-to-date weather information. Respond to user
                queries concisely and clearly. Use simple language and avoid
                technical jargon. Provide temperature, precipitation, wind
                conditions, and any weather alerts. Include helpful tips if
                severe weather is expected.</role>
  source:
    openapi: evi-openapi.json
