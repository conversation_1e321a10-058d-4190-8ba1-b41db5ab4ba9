imports:
  root: __package__.yml
service:
  auth: false
  base-path: ''
  endpoints:
    list-tools:
      path: /v0/evi/tools
      method: GET
      auth: true
      docs: >-
        Fetches a paginated list of **Tools**.


        Refer to our [tool
        use](/docs/speech-to-speech-evi/features/tool-use#function-calling)
        guide for comprehensive instructions on defining and integrating tools
        into EVI.
      pagination:
        offset: $request.page_number
        results: $response.tools_page
      source:
        openapi: evi-openapi.json
      display-name: List tools
      request:
        name: ToolsListToolsRequest
        query-parameters:
          page_number:
            type: optional<integer>
            default: 0
            docs: >-
              Specifies the page number to retrieve, enabling pagination.


              This parameter uses zero-based indexing. For example, setting
              `page_number` to 0 retrieves the first page of results (items 0-9
              if `page_size` is 10), setting `page_number` to 1 retrieves the
              second page (items 10-19), and so on. Defaults to 0, which
              retrieves the first page.
          page_size:
            type: optional<integer>
            docs: >-
              Specifies the maximum number of results to include per page,
              enabling pagination. The value must be between 1 and 100,
              inclusive.


              For example, if `page_size` is set to 10, each page will include
              up to 10 items. Defaults to 10.
          restrict_to_most_recent:
            type: optional<boolean>
            docs: >-
              By default, `restrict_to_most_recent` is set to true, returning
              only the latest version of each tool. To include all versions of
              each tool in the list, set `restrict_to_most_recent` to false.
          name:
            type: optional<string>
            docs: Filter to only include tools with name.
      response:
        docs: Success
        type: root.ReturnPagedUserDefinedTools
        status-code: 200
      errors:
        - root.BadRequestError
      examples:
        - query-parameters:
            page_number: 0
            page_size: 2
          response:
            body:
              page_number: 0
              page_size: 2
              total_pages: 1
              tools_page:
                - tool_type: FUNCTION
                  id: d20827af-5d8d-4f66-b6b9-ce2e3e1ea2b2
                  version: 0
                  version_type: FIXED
                  version_description: Fetches user's current location.
                  name: get_current_location
                  created_on: 1715267200693
                  modified_on: 1715267200693
                  fallback_content: Unable to fetch location.
                  description: Fetches user's current location.
                  parameters: >-
                    { "type": "object", "properties": { "location": { "type":
                    "string", "description": "The city and state, e.g. San
                    Francisco, CA" }}, "required": ["location"] }
                - tool_type: FUNCTION
                  id: 4442f3ea-9038-40e3-a2ce-1522b7de770f
                  version: 0
                  version_type: FIXED
                  version_description: >-
                    Fetches current weather and uses celsius or fahrenheit based
                    on location of user.
                  name: get_current_weather
                  created_on: 1715266126705
                  modified_on: 1715266126705
                  fallback_content: Unable to fetch location.
                  description: >-
                    Fetches current weather and uses celsius or fahrenheit based
                    on location of user.
                  parameters: >-
                    { "type": "object", "properties": { "location": { "type":
                    "string", "description": "The city and state, e.g. San
                    Francisco, CA" }, "format": { "type": "string", "enum":
                    ["celsius", "fahrenheit"], "description": "The temperature
                    unit to use. Infer this from the users location." } },
                    "required": ["location", "format"] }
    create-tool:
      path: /v0/evi/tools
      method: POST
      auth: true
      docs: >-
        Creates a **Tool** that can be added to an [EVI
        configuration](/reference/speech-to-speech-evi/configs/create-config).


        Refer to our [tool
        use](/docs/speech-to-speech-evi/features/tool-use#function-calling)
        guide for comprehensive instructions on defining and integrating tools
        into EVI.
      source:
        openapi: evi-openapi.json
      display-name: Create tool
      request:
        name: PostedUserDefinedTool
        body:
          properties:
            name:
              type: string
              docs: Name applied to all versions of a particular Tool.
            version_description:
              type: optional<string>
              docs: An optional description of the Tool version.
            description:
              type: optional<string>
              docs: >-
                An optional description of what the Tool does, used by the
                supplemental LLM to choose when and how to call the function.
            parameters:
              type: string
              docs: >-
                Stringified JSON defining the parameters used by this version of
                the Tool.


                These parameters define the inputs needed for the Tool’s
                execution, including the expected data type and description for
                each input field. Structured as a stringified JSON schema, this
                format ensures the Tool receives data in the expected format.
            fallback_content:
              type: optional<string>
              docs: >-
                Optional text passed to the supplemental LLM in place of the
                tool call result. The LLM then uses this text to generate a
                response back to the user, ensuring continuity in the
                conversation if the Tool errors.
        content-type: application/json
      response:
        docs: Created
        type: optional<root.ReturnUserDefinedTool>
        status-code: 201
      errors:
        - root.BadRequestError
      examples:
        - request:
            name: get_current_weather
            parameters: >-
              { "type": "object", "properties": { "location": { "type":
              "string", "description": "The city and state, e.g. San Francisco,
              CA" }, "format": { "type": "string", "enum": ["celsius",
              "fahrenheit"], "description": "The temperature unit to use. Infer
              this from the users location." } }, "required": ["location",
              "format"] }
            version_description: >-
              Fetches current weather and uses celsius or fahrenheit based on
              location of user.
            description: This tool is for getting the current weather.
            fallback_content: Unable to fetch current weather.
          response:
            body:
              tool_type: FUNCTION
              id: aa9b71c4-723c-47ff-9f83-1a1829e74376
              version: 0
              version_type: FIXED
              version_description: >-
                Fetches current weather and uses celsius or fahrenheit based on
                location of user.
              name: get_current_weather
              created_on: 1715275452390
              modified_on: 1715275452390
              fallback_content: Unable to fetch current weather.
              description: This tool is for getting the current weather.
              parameters: >-
                { "type": "object", "properties": { "location": { "type":
                "string", "description": "The city and state, e.g. San
                Francisco, CA" }, "format": { "type": "string", "enum":
                ["celsius", "fahrenheit"], "description": "The temperature unit
                to use. Infer this from the users location." } }, "required":
                ["location", "format"] }
    list-tool-versions:
      path: /v0/evi/tools/{id}
      method: GET
      auth: true
      docs: >-
        Fetches a list of a **Tool's** versions.


        Refer to our [tool
        use](/docs/speech-to-speech-evi/features/tool-use#function-calling)
        guide for comprehensive instructions on defining and integrating tools
        into EVI.
      pagination:
        offset: $request.page_number
        results: $response.tools_page
      source:
        openapi: evi-openapi.json
      path-parameters:
        id:
          type: string
          docs: Identifier for a Tool. Formatted as a UUID.
      display-name: List tool versions
      request:
        name: ToolsListToolVersionsRequest
        query-parameters:
          page_number:
            type: optional<integer>
            default: 0
            docs: >-
              Specifies the page number to retrieve, enabling pagination.


              This parameter uses zero-based indexing. For example, setting
              `page_number` to 0 retrieves the first page of results (items 0-9
              if `page_size` is 10), setting `page_number` to 1 retrieves the
              second page (items 10-19), and so on. Defaults to 0, which
              retrieves the first page.
          page_size:
            type: optional<integer>
            docs: >-
              Specifies the maximum number of results to include per page,
              enabling pagination. The value must be between 1 and 100,
              inclusive.


              For example, if `page_size` is set to 10, each page will include
              up to 10 items. Defaults to 10.
          restrict_to_most_recent:
            type: optional<boolean>
            docs: >-
              By default, `restrict_to_most_recent` is set to true, returning
              only the latest version of each tool. To include all versions of
              each tool in the list, set `restrict_to_most_recent` to false.
      response:
        docs: Success
        type: root.ReturnPagedUserDefinedTools
        status-code: 200
      errors:
        - root.BadRequestError
      examples:
        - path-parameters:
            id: 00183a3f-79ba-413d-9f3b-609864268bea
          response:
            body:
              page_number: 0
              page_size: 10
              total_pages: 1
              tools_page:
                - tool_type: FUNCTION
                  id: 00183a3f-79ba-413d-9f3b-609864268bea
                  version: 1
                  version_type: FIXED
                  version_description: >-
                    Fetches current weather and uses celsius, fahrenheit, or
                    kelvin based on location of user.
                  name: get_current_weather
                  created_on: 1715277014228
                  modified_on: 1715277602313
                  fallback_content: Unable to fetch current weather.
                  description: This tool is for getting the current weather.
                  parameters: >-
                    { "type": "object", "properties": { "location": { "type":
                    "string", "description": "The city and state, e.g. San
                    Francisco, CA" }, "format": { "type": "string", "enum":
                    ["celsius", "fahrenheit", "kelvin"], "description": "The
                    temperature unit to use. Infer this from the users
                    location." } }, "required": ["location", "format"] }
    create-tool-version:
      path: /v0/evi/tools/{id}
      method: POST
      auth: true
      docs: >-
        Updates a **Tool** by creating a new version of the **Tool**.


        Refer to our [tool
        use](/docs/speech-to-speech-evi/features/tool-use#function-calling)
        guide for comprehensive instructions on defining and integrating tools
        into EVI.
      source:
        openapi: evi-openapi.json
      path-parameters:
        id:
          type: string
          docs: Identifier for a Tool. Formatted as a UUID.
      display-name: Create tool version
      request:
        name: PostedUserDefinedToolVersion
        body:
          properties:
            version_description:
              type: optional<string>
              docs: An optional description of the Tool version.
            description:
              type: optional<string>
              docs: >-
                An optional description of what the Tool does, used by the
                supplemental LLM to choose when and how to call the function.
            parameters:
              type: string
              docs: >-
                Stringified JSON defining the parameters used by this version of
                the Tool.


                These parameters define the inputs needed for the Tool’s
                execution, including the expected data type and description for
                each input field. Structured as a stringified JSON schema, this
                format ensures the Tool receives data in the expected format.
            fallback_content:
              type: optional<string>
              docs: >-
                Optional text passed to the supplemental LLM in place of the
                tool call result. The LLM then uses this text to generate a
                response back to the user, ensuring continuity in the
                conversation if the Tool errors.
        content-type: application/json
      response:
        docs: Created
        type: optional<root.ReturnUserDefinedTool>
        status-code: 201
      errors:
        - root.BadRequestError
      examples:
        - path-parameters:
            id: 00183a3f-79ba-413d-9f3b-609864268bea
          request:
            parameters: >-
              { "type": "object", "properties": { "location": { "type":
              "string", "description": "The city and state, e.g. San Francisco,
              CA" }, "format": { "type": "string", "enum": ["celsius",
              "fahrenheit", "kelvin"], "description": "The temperature unit to
              use. Infer this from the users location." } }, "required":
              ["location", "format"] }
            version_description: >-
              Fetches current weather and uses celsius, fahrenheit, or kelvin
              based on location of user.
            fallback_content: Unable to fetch current weather.
            description: This tool is for getting the current weather.
          response:
            body:
              tool_type: FUNCTION
              id: 00183a3f-79ba-413d-9f3b-609864268bea
              version: 1
              version_type: FIXED
              version_description: >-
                Fetches current weather and uses celsius, fahrenheit, or kelvin
                based on location of user.
              name: get_current_weather
              created_on: 1715277014228
              modified_on: 1715277602313
              fallback_content: Unable to fetch current weather.
              description: This tool is for getting the current weather.
              parameters: >-
                { "type": "object", "properties": { "location": { "type":
                "string", "description": "The city and state, e.g. San
                Francisco, CA" }, "format": { "type": "string", "enum":
                ["celsius", "fahrenheit", "kelvin"], "description": "The
                temperature unit to use. Infer this from the users location." }
                }, "required": ["location", "format"] }
    delete-tool:
      path: /v0/evi/tools/{id}
      method: DELETE
      auth: true
      docs: >-
        Deletes a **Tool** and its versions.


        Refer to our [tool
        use](/docs/speech-to-speech-evi/features/tool-use#function-calling)
        guide for comprehensive instructions on defining and integrating tools
        into EVI.
      source:
        openapi: evi-openapi.json
      path-parameters:
        id:
          type: string
          docs: Identifier for a Tool. Formatted as a UUID.
      display-name: Delete tool
      errors:
        - root.BadRequestError
      examples:
        - path-parameters:
            id: 00183a3f-79ba-413d-9f3b-609864268bea
    update-tool-name:
      path: /v0/evi/tools/{id}
      method: PATCH
      auth: true
      docs: >-
        Updates the name of a **Tool**.


        Refer to our [tool
        use](/docs/speech-to-speech-evi/features/tool-use#function-calling)
        guide for comprehensive instructions on defining and integrating tools
        into EVI.
      source:
        openapi: evi-openapi.json
      path-parameters:
        id:
          type: string
          docs: Identifier for a Tool. Formatted as a UUID.
      display-name: Update tool name
      request:
        name: PostedUserDefinedToolName
        body:
          properties:
            name:
              type: string
              docs: Name applied to all versions of a particular Tool.
        content-type: application/json
      response:
        docs: Success
        type: text
        status-code: 200
      errors:
        - root.BadRequestError
      examples:
        - path-parameters:
            id: 00183a3f-79ba-413d-9f3b-609864268bea
          request:
            name: get_current_temperature
    get-tool-version:
      path: /v0/evi/tools/{id}/version/{version}
      method: GET
      auth: true
      docs: >-
        Fetches a specified version of a **Tool**.


        Refer to our [tool
        use](/docs/speech-to-speech-evi/features/tool-use#function-calling)
        guide for comprehensive instructions on defining and integrating tools
        into EVI.
      source:
        openapi: evi-openapi.json
      path-parameters:
        id:
          type: string
          docs: Identifier for a Tool. Formatted as a UUID.
        version:
          type: integer
          docs: >-
            Version number for a Tool.


            Tools, Configs, Custom Voices, and Prompts are versioned. This
            versioning system supports iterative development, allowing you to
            progressively refine tools and revert to previous versions if
            needed.


            Version numbers are integer values representing different iterations
            of the Tool. Each update to the Tool increments its version number.
      display-name: Get tool version
      response:
        docs: Success
        type: optional<root.ReturnUserDefinedTool>
        status-code: 200
      errors:
        - root.BadRequestError
      examples:
        - path-parameters:
            id: 00183a3f-79ba-413d-9f3b-609864268bea
            version: 1
          response:
            body:
              tool_type: FUNCTION
              id: 00183a3f-79ba-413d-9f3b-609864268bea
              version: 1
              version_type: FIXED
              version_description: >-
                Fetches current weather and uses celsius, fahrenheit, or kelvin
                based on location of user.
              name: string
              created_on: 1715277014228
              modified_on: 1715277602313
              fallback_content: Unable to fetch current weather.
              description: This tool is for getting the current weather.
              parameters: >-
                { "type": "object", "properties": { "location": { "type":
                "string", "description": "The city and state, e.g. San
                Francisco, CA" }, "format": { "type": "string", "enum":
                ["celsius", "fahrenheit", "kelvin"], "description": "The
                temperature unit to use. Infer this from the users location." }
                }, "required": ["location", "format"] }
    delete-tool-version:
      path: /v0/evi/tools/{id}/version/{version}
      method: DELETE
      auth: true
      docs: >-
        Deletes a specified version of a **Tool**.


        Refer to our [tool
        use](/docs/speech-to-speech-evi/features/tool-use#function-calling)
        guide for comprehensive instructions on defining and integrating tools
        into EVI.
      source:
        openapi: evi-openapi.json
      path-parameters:
        id:
          type: string
          docs: Identifier for a Tool. Formatted as a UUID.
        version:
          type: integer
          docs: >-
            Version number for a Tool.


            Tools, Configs, Custom Voices, and Prompts are versioned. This
            versioning system supports iterative development, allowing you to
            progressively refine tools and revert to previous versions if
            needed.


            Version numbers are integer values representing different iterations
            of the Tool. Each update to the Tool increments its version number.
      display-name: Delete tool version
      errors:
        - root.BadRequestError
      examples:
        - path-parameters:
            id: 00183a3f-79ba-413d-9f3b-609864268bea
            version: 1
    update-tool-description:
      path: /v0/evi/tools/{id}/version/{version}
      method: PATCH
      auth: true
      docs: >-
        Updates the description of a specified **Tool** version.


        Refer to our [tool
        use](/docs/speech-to-speech-evi/features/tool-use#function-calling)
        guide for comprehensive instructions on defining and integrating tools
        into EVI.
      source:
        openapi: evi-openapi.json
      path-parameters:
        id:
          type: string
          docs: Identifier for a Tool. Formatted as a UUID.
        version:
          type: integer
          docs: >-
            Version number for a Tool.


            Tools, Configs, Custom Voices, and Prompts are versioned. This
            versioning system supports iterative development, allowing you to
            progressively refine tools and revert to previous versions if
            needed.


            Version numbers are integer values representing different iterations
            of the Tool. Each update to the Tool increments its version number.
      display-name: Update tool description
      request:
        name: PostedUserDefinedToolVersionDescription
        body:
          properties:
            version_description:
              type: optional<string>
              docs: An optional description of the Tool version.
        content-type: application/json
      response:
        docs: Success
        type: optional<root.ReturnUserDefinedTool>
        status-code: 200
      errors:
        - root.BadRequestError
      examples:
        - path-parameters:
            id: 00183a3f-79ba-413d-9f3b-609864268bea
            version: 1
          request:
            version_description: >-
              Fetches current temperature, precipitation, wind speed, AQI, and
              other weather conditions. Uses Celsius, Fahrenheit, or kelvin
              depending on user's region.
          response:
            body:
              tool_type: FUNCTION
              id: 00183a3f-79ba-413d-9f3b-609864268bea
              version: 1
              version_type: FIXED
              version_description: >-
                Fetches current temperature, precipitation, wind speed, AQI, and
                other weather conditions. Uses Celsius, Fahrenheit, or kelvin
                depending on user's region.
              name: string
              created_on: 1715277014228
              modified_on: 1715277602313
              fallback_content: Unable to fetch current weather.
              description: This tool is for getting the current weather.
              parameters: >-
                { "type": "object", "properties": { "location": { "type":
                "string", "description": "The city and state, e.g. San
                Francisco, CA" }, "format": { "type": "string", "enum":
                ["celsius", "fahrenheit", "kelvin"], "description": "The
                temperature unit to use. Infer this from the users location." }
                }, "required": ["location", "format"] }
  source:
    openapi: evi-openapi.json
