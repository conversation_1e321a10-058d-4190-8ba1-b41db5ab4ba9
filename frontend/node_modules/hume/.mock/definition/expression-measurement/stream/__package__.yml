types:
  EmotionEmbeddingItem:
    properties:
      name:
        type: optional<string>
        docs: Name of the emotion being expressed.
      score:
        type: optional<double>
        docs: Embedding value for the emotion being expressed.
    source:
      openapi: streaming-asyncapi.yml
  EmotionEmbedding:
    docs: A high-dimensional embedding in emotion space.
    type: list<EmotionEmbeddingItem>
  StreamBoundingBox:
    docs: A bounding box around a face.
    properties:
      x:
        type: optional<double>
        docs: x-coordinate of bounding box top left corner.
        validation:
          min: 0
      'y':
        type: optional<double>
        docs: y-coordinate of bounding box top left corner.
        validation:
          min: 0
      w:
        type: optional<double>
        docs: Bounding box width.
        validation:
          min: 0
      h:
        type: optional<double>
        docs: Bounding box height.
        validation:
          min: 0
    source:
      openapi: streaming-asyncapi.yml
  TimeRange:
    docs: A time range with a beginning and end, measured in seconds.
    properties:
      begin:
        type: optional<double>
        docs: Beginning of time range in seconds.
        validation:
          min: 0
      end:
        type: optional<double>
        docs: End of time range in seconds.
        validation:
          min: 0
    source:
      openapi: streaming-asyncapi.yml
  TextPosition:
    docs: >
      Position of a segment of text within a larger document, measured in
      characters. Uses zero-based indexing. The beginning index is inclusive and
      the end index is exclusive.
    properties:
      begin:
        type: optional<double>
        docs: The index of the first character in the text segment, inclusive.
        validation:
          min: 0
      end:
        type: optional<double>
        docs: The index of the last character in the text segment, exclusive.
        validation:
          min: 0
    source:
      openapi: streaming-asyncapi.yml
  SentimentItem:
    properties:
      name:
        type: optional<string>
        docs: Level of sentiment, ranging from 1 (negative) to 9 (positive)
      score:
        type: optional<double>
        docs: Prediction for this level of sentiment
    source:
      openapi: streaming-asyncapi.yml
  Sentiment:
    docs: >-
      Sentiment predictions returned as a distribution. This model predicts the
      probability that a given text could be interpreted as having each
      sentiment level from 1 (negative) to 9 (positive).


      Compared to returning one estimate of sentiment, this enables a more
      nuanced analysis of a text's meaning. For example, a text with very
      neutral sentiment would have an average rating of 5. But also a text that
      could be interpreted as having very positive sentiment or very negative
      sentiment would also have an average rating of 5. The average sentiment is
      less informative than the distribution over sentiment, so this API returns
      a value for each sentiment level.
    type: list<SentimentItem>
  ToxicityItem:
    properties:
      name:
        type: optional<string>
        docs: Category of toxicity.
      score:
        type: optional<double>
        docs: Prediction for this category of toxicity
    source:
      openapi: streaming-asyncapi.yml
  Toxicity:
    docs: >-
      Toxicity predictions returned as probabilities that the text can be
      classified into the following categories: toxic, severe_toxic, obscene,
      threat, insult, and identity_hate.
    type: list<ToxicityItem>
