channel:
  path: /models
  url: prod
  auth: false
  headers:
    X-Hume-Api-Key:
      type: string
      name: hume<PERSON><PERSON><PERSON>ey
  messages:
    publish:
      origin: client
      body:
        type: StreamModelsEndpointPayload
        docs: Models endpoint payload
    subscribe:
      origin: server
      body: SubscribeEvent
  examples:
    - messages:
        - type: publish
          body: {}
        - type: subscribe
          body: {}
types:
  StreamFace:
    docs: >
      Configuration for the facial expression emotion model.


      Note: Using the `reset_stream` parameter does not have any effect on face
      identification. A single face identifier cache is maintained over a full
      session whether `reset_stream` is used or not.
    properties:
      facs:
        type: optional<map<string, unknown>>
        docs: >-
          Configuration for FACS predictions. If missing or null, no FACS
          predictions will be generated.
      descriptions:
        type: optional<map<string, unknown>>
        docs: >-
          Configuration for Descriptions predictions. If missing or null, no
          Descriptions predictions will be generated.
      identify_faces:
        type: optional<boolean>
        docs: >
          Whether to return identifiers for faces across frames. If true, unique
          identifiers will be assigned to face bounding boxes to differentiate
          different faces. If false, all faces will be tagged with an "unknown"
          ID.
        default: false
      fps_pred:
        type: optional<double>
        docs: >
          Number of frames per second to process. Other frames will be omitted
          from the response.
        default: 3
      prob_threshold:
        type: optional<double>
        docs: >
          Face detection probability threshold. Faces detected with a
          probability less than this threshold will be omitted from the
          response.
        default: 3
      min_face_size:
        type: optional<double>
        docs: >
          Minimum bounding box side length in pixels to treat as a face. Faces
          detected with a bounding box side length in pixels less than this
          threshold will be omitted from the response.
        default: 3
    source:
      openapi: streaming-asyncapi.yml
    inline: true
  StreamLanguage:
    docs: Configuration for the language emotion model.
    properties:
      sentiment:
        type: optional<map<string, unknown>>
        docs: >-
          Configuration for sentiment predictions. If missing or null, no
          sentiment predictions will be generated.
      toxicity:
        type: optional<map<string, unknown>>
        docs: >-
          Configuration for toxicity predictions. If missing or null, no
          toxicity predictions will be generated.
      granularity:
        type: optional<string>
        docs: >-
          The granularity at which to generate predictions. Values are `word`,
          `sentence`, `utterance`, or `passage`. To get a single prediction for
          the entire text of your streaming payload use `passage`. Default value
          is `word`.
    source:
      openapi: streaming-asyncapi.yml
    inline: true
  Config:
    docs: >
      Configuration used to specify which models should be used and with what
      settings.
    properties:
      burst:
        type: optional<map<string, unknown>>
        docs: |
          Configuration for the vocal burst emotion model.

          Note: Model configuration is not currently available in streaming.

          Please use the default configuration by passing an empty object `{}`.
      face:
        type: optional<StreamFace>
        docs: >
          Configuration for the facial expression emotion model.


          Note: Using the `reset_stream` parameter does not have any effect on
          face identification. A single face identifier cache is maintained over
          a full session whether `reset_stream` is used or not.
      facemesh:
        type: optional<map<string, unknown>>
        docs: |
          Configuration for the facemesh emotion model.

          Note: Model configuration is not currently available in streaming.

          Please use the default configuration by passing an empty object `{}`.
      language:
        type: optional<StreamLanguage>
        docs: Configuration for the language emotion model.
      prosody:
        type: optional<map<string, unknown>>
        docs: |
          Configuration for the speech prosody emotion model.

          Note: Model configuration is not currently available in streaming.

          Please use the default configuration by passing an empty object `{}`.
    source:
      openapi: streaming-asyncapi.yml
    inline: true
  StreamModelsEndpointPayload:
    docs: Models endpoint payload
    properties:
      data:
        type: optional<string>
      models:
        type: optional<Config>
        docs: >
          Configuration used to specify which models should be used and with
          what settings.
      stream_window_ms:
        type: optional<double>
        docs: >
          Length in milliseconds of streaming sliding window.


          Extending the length of this window will prepend media context from
          past payloads into the current payload.


          For example, if on the first payload you send 500ms of data and on the
          second payload you send an additional 500ms of data, a window of at
          least 1000ms will allow the model to process all 1000ms of stream
          data.


          A window of 600ms would append the full 500ms of the second payload to
          the last 100ms of the first payload.


          Note: This feature is currently only supported for audio data and
          audio models. For other file types and models this parameter will be
          ignored.
        default: 5000
        validation:
          min: 500
          max: 10000
      reset_stream:
        type: optional<boolean>
        docs: >
          Whether to reset the streaming sliding window before processing the
          current payload.


          If this parameter is set to `true` then past context will be deleted
          before processing the current payload.


          Use reset_stream when one audio file is done being processed and you
          do not want context to leak across files.
        default: false
      raw_text:
        type: optional<boolean>
        docs: >
          Set to `true` to enable the data parameter to be parsed as raw text
          rather than base64 encoded bytes.

          This parameter is useful if you want to send text to be processed by
          the language model, but it cannot be used with other file types like
          audio, image, or video.
        default: false
      job_details:
        type: optional<boolean>
        docs: >
          Set to `true` to get details about the job.


          This parameter can be set in the same payload as data or it can be set
          without data and models configuration to get the job details between
          payloads.


          This parameter is useful to get the unique job ID.
        default: false
      payload_id:
        type: optional<string>
        docs: >
          Pass an arbitrary string as the payload ID and get it back at the top
          level of the socket response.


          This can be useful if you have multiple requests running
          asynchronously and want to disambiguate responses as they are
          received.
    source:
      openapi: streaming-asyncapi.yml
  StreamModelPredictionsJobDetails:
    docs: >
      If the job_details flag was set in the request, details about the current
      streaming job will be returned in the response body.
    properties:
      job_id:
        type: optional<string>
        docs: ID of the current streaming job.
    source:
      openapi: streaming-asyncapi.yml
    inline: true
  StreamModelPredictionsBurstPredictionsItem:
    properties:
      time: optional<streamRoot.TimeRange>
      emotions: optional<streamRoot.EmotionEmbedding>
    source:
      openapi: streaming-asyncapi.yml
    inline: true
  StreamModelPredictionsBurst:
    docs: Response for the vocal burst emotion model.
    properties:
      predictions: optional<list<StreamModelPredictionsBurstPredictionsItem>>
    source:
      openapi: streaming-asyncapi.yml
    inline: true
  StreamModelPredictionsFacePredictionsItem:
    properties:
      frame:
        type: optional<double>
        docs: Frame number
      time:
        type: optional<double>
        docs: Time in seconds when face detection occurred.
      bbox: optional<streamRoot.StreamBoundingBox>
      prob:
        type: optional<double>
        docs: The predicted probability that a detected face was actually a face.
      face_id:
        type: optional<string>
        docs: >-
          Identifier for a face. Not that this defaults to `unknown` unless face
          identification is enabled in the face model configuration.
      emotions: optional<streamRoot.EmotionEmbedding>
      facs: optional<streamRoot.EmotionEmbedding>
      descriptions: optional<streamRoot.EmotionEmbedding>
    source:
      openapi: streaming-asyncapi.yml
    inline: true
  StreamModelPredictionsFace:
    docs: Response for the facial expression emotion model.
    properties:
      predictions: optional<list<StreamModelPredictionsFacePredictionsItem>>
    source:
      openapi: streaming-asyncapi.yml
    inline: true
  StreamModelPredictionsFacemeshPredictionsItem:
    properties:
      emotions: optional<streamRoot.EmotionEmbedding>
    source:
      openapi: streaming-asyncapi.yml
    inline: true
  StreamModelPredictionsFacemesh:
    docs: Response for the facemesh emotion model.
    properties:
      predictions: optional<list<StreamModelPredictionsFacemeshPredictionsItem>>
    source:
      openapi: streaming-asyncapi.yml
    inline: true
  StreamModelPredictionsLanguagePredictionsItem:
    properties:
      text:
        type: optional<string>
        docs: A segment of text (like a word or a sentence).
      position: optional<streamRoot.TextPosition>
      emotions: optional<streamRoot.EmotionEmbedding>
      sentiment: optional<streamRoot.Sentiment>
      toxicity: optional<streamRoot.Toxicity>
    source:
      openapi: streaming-asyncapi.yml
    inline: true
  StreamModelPredictionsLanguage:
    docs: Response for the language emotion model.
    properties:
      predictions: optional<list<StreamModelPredictionsLanguagePredictionsItem>>
    source:
      openapi: streaming-asyncapi.yml
    inline: true
  StreamModelPredictionsProsodyPredictionsItem:
    properties:
      time: optional<streamRoot.TimeRange>
      emotions: optional<streamRoot.EmotionEmbedding>
    source:
      openapi: streaming-asyncapi.yml
    inline: true
  StreamModelPredictionsProsody:
    docs: Response for the speech prosody emotion model.
    properties:
      predictions: optional<list<StreamModelPredictionsProsodyPredictionsItem>>
    source:
      openapi: streaming-asyncapi.yml
    inline: true
  StreamModelPredictions:
    docs: Model predictions
    properties:
      payload_id:
        type: optional<string>
        docs: >
          If a payload ID was passed in the request, the same payload ID will be
          sent back in the response body.
      job_details:
        type: optional<StreamModelPredictionsJobDetails>
        docs: >
          If the job_details flag was set in the request, details about the
          current streaming job will be returned in the response body.
      burst:
        type: optional<StreamModelPredictionsBurst>
        docs: Response for the vocal burst emotion model.
      face:
        type: optional<StreamModelPredictionsFace>
        docs: Response for the facial expression emotion model.
      facemesh:
        type: optional<StreamModelPredictionsFacemesh>
        docs: Response for the facemesh emotion model.
      language:
        type: optional<StreamModelPredictionsLanguage>
        docs: Response for the language emotion model.
      prosody:
        type: optional<StreamModelPredictionsProsody>
        docs: Response for the speech prosody emotion model.
    source:
      openapi: streaming-asyncapi.yml
    inline: true
  JobDetails:
    docs: >
      If the job_details flag was set in the request, details about the current
      streaming job will be returned in the response body.
    properties:
      job_id:
        type: optional<string>
        docs: ID of the current streaming job.
    source:
      openapi: streaming-asyncapi.yml
    inline: true
  StreamErrorMessage:
    docs: Error message
    properties:
      error:
        type: optional<string>
        docs: Error message text.
      code:
        type: optional<string>
        docs: Unique identifier for the error.
      payload_id:
        type: optional<string>
        docs: >
          If a payload ID was passed in the request, the same payload ID will be
          sent back in the response body.
      job_details:
        type: optional<JobDetails>
        docs: >
          If the job_details flag was set in the request, details about the
          current streaming job will be returned in the response body.
    source:
      openapi: streaming-asyncapi.yml
    inline: true
  StreamWarningMessageJobDetails:
    docs: >
      If the job_details flag was set in the request, details about the current
      streaming job will be returned in the response body.
    properties:
      job_id:
        type: optional<string>
        docs: ID of the current streaming job.
    source:
      openapi: streaming-asyncapi.yml
    inline: true
  StreamWarningMessage:
    docs: Warning message
    properties:
      warning:
        type: optional<string>
        docs: Warning message text.
      code:
        type: optional<string>
        docs: Unique identifier for the error.
      payload_id:
        type: optional<string>
        docs: >
          If a payload ID was passed in the request, the same payload ID will be
          sent back in the response body.
      job_details:
        type: optional<StreamWarningMessageJobDetails>
        docs: >
          If the job_details flag was set in the request, details about the
          current streaming job will be returned in the response body.
    source:
      openapi: streaming-asyncapi.yml
    inline: true
  SubscribeEvent:
    discriminated: false
    union:
      - type: StreamModelPredictions
        docs: Model predictions
      - type: StreamErrorMessage
        docs: Error message
      - type: StreamWarningMessage
        docs: Warning message
    source:
      openapi: streaming-asyncapi.yml
imports:
  streamRoot: __package__.yml
