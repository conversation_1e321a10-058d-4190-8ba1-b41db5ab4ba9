errors:
  UnprocessableEntityError:
    status-code: 422
    type: HTTPValidationError
    docs: Validation Error
    examples:
      - value: {}
  BadRequestError:
    status-code: 400
    type: ErrorResponse
    docs: Bad Request
    examples:
      - value: {}
service:
  auth: false
  base-path: ''
  endpoints:
    synthesize-json:
      path: /v0/tts
      method: POST
      auth: true
      docs: >-
        Synthesizes one or more input texts into speech using the specified
        voice. If no voice is provided, a novel voice will be generated
        dynamically. Optionally, additional context can be included to influence
        the speech's style and prosody.


        The response includes the base64-encoded audio and metadata in JSON
        format.
      source:
        openapi: tts-openapi.yml
      display-name: Text-to-speech (Json)
      request:
        body:
          type: PostedTts
        query-parameters:
          access_token:
            type: optional<string>
            default: ''
            docs: >-
              Access token used for authenticating the client. If not provided,
              an `api_key` must be provided to authenticate.


              The access token is generated using both an API key and a Secret
              key, which provides an additional layer of security compared to
              using just an API key.


              For more details, refer to the [Authentication Strategies
              Guide](/docs/introduction/api-key#authentication-strategies).
        name: SynthesizeJsonRequest
        content-type: application/json
      response:
        docs: Successful Response
        type: ReturnTts
        status-code: 200
      errors:
        - UnprocessableEntityError
      examples:
        - request:
            utterances:
              - text: >-
                  Beauty is no quality in things themselves: It exists merely in
                  the mind which contemplates them.
                description: >-
                  Middle-aged masculine voice with a clear, rhythmic Scots lilt,
                  rounded vowels, and a warm, steady tone with an articulate,
                  academic quality.
            context:
              utterances:
                - text: How can people see beauty so differently?
                  description: >-
                    A curious student with a clear and respectful tone, seeking
                    clarification on Hume's ideas with a straightforward
                    question.
            format:
              type: mp3
            num_generations: 1
          response:
            body:
              generations:
                - generation_id: 795c949a-1510-4a80-9646-7d0863b023ab
                  duration: 7.44225
                  file_size: 120192
                  encoding:
                    format: mp3
                    sample_rate: 48000
                  audio: //PExAA0DDYRvkpNfhv3JI5JZ...etc.
                  snippets:
                    - - audio: //PExAA0DDYRvkpNfhv3JI5JZ...etc.
                        generation_id: 795c949a-1510-4a80-9646-7d0863b023ab
                        id: 37b1b1b1-1b1b-1b1b-1b1b-1b1b1b1b1b1b
                        text: >-
                          Beauty is no quality in things themselves: It exists
                          merely in the mind which contemplates them.
                        utterance_index: 0
              request_id: 66e01f90-4501-4aa0-bbaf-74f45dc15aa725906
    synthesize-file:
      path: /v0/tts/file
      method: POST
      auth: true
      docs: >-
        Synthesizes one or more input texts into speech using the specified
        voice. If no voice is provided, a novel voice will be generated
        dynamically. Optionally, additional context can be included to influence
        the speech's style and prosody. 


        The response contains the generated audio file in the requested format.
      source:
        openapi: tts-openapi.yml
      display-name: Text-to-speech (File)
      request:
        body:
          type: PostedTts
        content-type: application/json
      response:
        docs: OK
        type: file
        status-code: 200
      errors:
        - UnprocessableEntityError
      examples:
        - request:
            utterances:
              - text: >-
                  Beauty is no quality in things themselves: It exists merely in
                  the mind which contemplates them.
                description: >-
                  Middle-aged masculine voice with a clear, rhythmic Scots lilt,
                  rounded vowels, and a warm, steady tone with an articulate,
                  academic quality.
            context:
              generation_id: 09ad914d-8e7f-40f8-a279-e34f07f7dab2
            format:
              type: mp3
            num_generations: 1
    synthesize-file-streaming:
      path: /v0/tts/stream/file
      method: POST
      auth: true
      docs: >-
        Streams synthesized speech using the specified voice. If no voice is
        provided, a novel voice will be generated dynamically. Optionally,
        additional context can be included to influence the speech's style and
        prosody.
      source:
        openapi: tts-openapi.yml
      display-name: Text-to-speech (Streamed File)
      request:
        body:
          type: PostedTts
        content-type: application/json
      response:
        docs: OK
        type: file
        status-code: 200
      errors:
        - UnprocessableEntityError
      examples:
        - request:
            utterances:
              - text: >-
                  Beauty is no quality in things themselves: It exists merely in
                  the mind which contemplates them.
                voice:
                  name: Male English Actor
                  provider: HUME_AI
    synthesize-json-streaming:
      path: /v0/tts/stream/json
      method: POST
      auth: true
      docs: >-
        Streams synthesized speech using the specified voice. If no voice is
        provided, a novel voice will be generated dynamically. Optionally,
        additional context can be included to influence the speech's style and
        prosody. 


        The response is a stream of JSON objects including audio encoded in
        base64.
      source:
        openapi: tts-openapi.yml
      display-name: Text-to-speech (Streamed JSON)
      request:
        body:
          type: PostedTts
        content-type: application/json
      response-stream:
        docs: Successful Response
        type: SnippetAudioChunk
        format: json
      errors:
        - UnprocessableEntityError
      examples:
        - request:
            utterances:
              - text: >-
                  Beauty is no quality in things themselves: It exists merely in
                  the mind which contemplates them.
                voice:
                  name: Male English Actor
                  provider: HUME_AI
  source:
    openapi: tts-openapi.yml
types:
  PostedContextWithGenerationId:
    properties:
      generation_id:
        type: string
        docs: >-
          The ID of a prior TTS generation to use as context for generating
          consistent speech style and prosody across multiple requests.
          Including context may increase audio generation times.
    source:
      openapi: tts-openapi.yml
  PostedContextWithUtterances:
    properties:
      utterances:
        type: list<PostedUtterance>
    source:
      openapi: tts-openapi.yml
  AudioEncoding:
    docs: >-
      Encoding information about the generated audio, including the `format` and
      `sample_rate`.
    properties:
      format:
        type: AudioFormatType
        docs: Format for the output audio.
      sample_rate:
        type: integer
        docs: >-
          The sample rate (`Hz`) of the generated audio. The default sample rate
          is `48000 Hz`.
    source:
      openapi: tts-openapi.yml
  AudioFormatType:
    enum:
      - mp3
      - pcm
      - wav
    source:
      openapi: tts-openapi.yml
  ReturnGeneration:
    properties:
      audio:
        type: string
        docs: >-
          The generated audio output in the requested format, encoded as a
          base64 string.
      duration:
        type: double
        docs: Duration of the generated audio in seconds.
      encoding:
        type: AudioEncoding
      file_size:
        type: integer
        docs: Size of the generated audio in bytes.
      generation_id:
        type: string
        docs: >-
          A unique ID associated with this TTS generation that can be used as
          context for generating consistent speech style and prosody across
          multiple requests.
      snippets:
        docs: >-
          A list of snippet groups where each group corresponds to an utterance
          in the request. Each group contains segmented snippets that represent
          the original utterance divided into more natural-sounding units
          optimized for speech delivery.
        type: list<list<Snippet>>
    source:
      openapi: tts-openapi.yml
  HTTPValidationError:
    properties:
      detail:
        type: optional<list<ValidationError>>
    source:
      openapi: tts-openapi.yml
  FormatMp3:
    properties: {}
    source:
      openapi: tts-openapi.yml
  PostedContext:
    discriminated: false
    docs: >-
      Utterances to use as context for generating consistent speech style and
      prosody across multiple requests. These will not be converted to speech
      output.
    union:
      - type: PostedContextWithGenerationId
      - type: PostedContextWithUtterances
    source:
      openapi: tts-openapi.yml
    inline: true
  Format:
    discriminant: type
    base-properties: {}
    docs: Specifies the output audio file format.
    union:
      mp3:
        type: FormatMp3
      pcm:
        type: FormatPcm
      wav:
        type: FormatWav
    source:
      openapi: tts-openapi.yml
  PostedTts:
    properties:
      context:
        type: optional<PostedContext>
        docs: >-
          Utterances to use as context for generating consistent speech style
          and prosody across multiple requests. These will not be converted to
          speech output.
      format:
        type: optional<Format>
        docs: Specifies the output audio file format.
      num_generations:
        type: optional<integer>
        docs: Number of generations of the audio to produce.
        default: 1
        validation:
          min: 1
          max: 5
      split_utterances:
        type: optional<boolean>
        docs: >-
          Controls how audio output is segmented in the response.


          - When **enabled** (`true`), input utterances are automatically split
          into natural-sounding speech segments.


          - When **disabled** (`false`), the response maintains a strict
          one-to-one mapping between input utterances and output snippets. 


          This setting affects how the `snippets` array is structured in the
          response, which may be important for applications that need to track
          the relationship between input text and generated audio segments. When
          setting to `false`, avoid including utterances with long `text`, as
          this can result in distorted output.
        default: true
      strip_headers:
        type: optional<boolean>
        docs: >-
          If enabled, the audio for all the chunks of a generation, once
          concatenated together, will constitute a single audio file. Otherwise,
          if disabled, each chunk's audio will be its own audio file, each with
          its own headers (if applicable).
        default: false
      utterances:
        docs: >-
          A list of **Utterances** to be converted to speech output.


          An **Utterance** is a unit of input for
          [Octave](/docs/text-to-speech-tts/overview), and includes input
          `text`, an optional `description` to serve as the prompt for how the
          speech should be delivered, an optional `voice` specification, and
          additional controls to guide delivery for `speed` and
          `trailing_silence`.
        type: list<PostedUtterance>
      instant_mode:
        type: optional<boolean>
        docs: >-
          Enables ultra-low latency streaming, significantly reducing the time
          until the first audio chunk is received. Recommended for real-time
          applications requiring immediate audio playback. For further details,
          see our documentation on [instant
          mode](/docs/text-to-speech-tts/overview#ultra-low-latency-streaming-instant-mode). 

          - A
          [voice](/reference/text-to-speech-tts/synthesize-json-streaming#request.body.utterances.voice)
          must be specified when instant mode is enabled. Dynamic voice
          generation is not supported with this mode.

          - Instant mode is only supported for streaming endpoints (e.g.,
          [/v0/tts/stream/json](/reference/text-to-speech-tts/synthesize-json-streaming),
          [/v0/tts/stream/file](/reference/text-to-speech-tts/synthesize-file-streaming)).

          - Ensure only a single generation is requested
          ([num_generations](/reference/text-to-speech-tts/synthesize-json-streaming#request.body.num_generations)
          must be `1` or omitted).
        default: true
    source:
      openapi: tts-openapi.yml
  ReturnTts:
    properties:
      generations:
        type: list<ReturnGeneration>
      request_id:
        type: optional<string>
        docs: >-
          A unique ID associated with this request for tracking and
          troubleshooting. Use this ID when contacting [support](/support) for
          troubleshooting assistance.
    source:
      openapi: tts-openapi.yml
  ReturnVoice:
    docs: An Octave voice available for text-to-speech
    properties:
      id:
        type: optional<string>
        docs: ID of the voice in the `Voice Library`.
      name:
        type: optional<string>
        docs: Name of the voice in the `Voice Library`.
      provider:
        type: optional<VoiceProvider>
        docs: >-
          The provider associated with the created voice.


          Voices created through this endpoint will always have the provider set
          to `CUSTOM_VOICE`, indicating a custom voice stored in your account.
    source:
      openapi: tts-openapi.yml
  FormatPcm:
    properties: {}
    source:
      openapi: tts-openapi.yml
  Snippet:
    properties:
      audio:
        type: string
        docs: >-
          The segmented audio output in the requested format, encoded as a
          base64 string.
      generation_id:
        type: string
        docs: The generation ID this snippet corresponds to.
      id:
        type: string
        docs: A unique ID associated with this **Snippet**.
      text:
        type: string
        docs: The text for this **Snippet**.
      transcribed_text:
        type: optional<string>
        docs: >-
          The transcribed text of the generated audio. It is only present if
          `instant_mode` is set to `false`.
      utterance_index:
        type: optional<integer>
        docs: The index of the utterance in the request this snippet corresponds to.
    source:
      openapi: tts-openapi.yml
  SnippetAudioChunk:
    properties: {}
    source:
      openapi: tts-openapi.yml
  PostedUtterance:
    properties:
      description:
        type: optional<string>
        docs: >-
          Natural language instructions describing how the synthesized speech
          should sound, including but not limited to tone, intonation, pacing,
          and accent.


          **This field behaves differently depending on whether a voice is
          specified**:

          - **Voice specified**: the description will serve as acting directions
          for delivery. Keep directions concise—100 characters or fewer—for best
          results. See our guide on [acting
          instructions](/docs/text-to-speech-tts/acting-instructions).

          - **Voice not specified**: the description will serve as a voice
          prompt for generating a voice. See our [prompting
          guide](/docs/text-to-speech-tts/prompting) for design tips.
        validation:
          maxLength: 1000
      speed:
        type: optional<double>
        docs: >-
          Speed multiplier for the synthesized speech. Extreme values below 0.75
          and above 1.5 may sometimes cause instability to the generated output.
        default: 1
        validation:
          min: 0.5
          max: 2
      text:
        type: string
        docs: The input text to be synthesized into speech.
        validation:
          maxLength: 5000
      trailing_silence:
        type: optional<double>
        docs: Duration of trailing silence (in seconds) to add to this utterance
        default: 0
        validation:
          min: 0
          max: 5
      voice:
        type: optional<PostedUtteranceVoice>
        docs: >-
          The `name` or `id` associated with a **Voice** from the **Voice
          Library** to be used as the speaker for this and all subsequent
          `utterances`, until the `voice` field is updated again.

           See our [voices guide](/docs/text-to-speech-tts/voices) for more details on generating and specifying **Voices**.
    source:
      openapi: tts-openapi.yml
  ValidationErrorLocItem:
    discriminated: false
    union:
      - string
      - integer
    source:
      openapi: tts-openapi.yml
    inline: true
  ValidationError:
    properties:
      loc:
        type: list<ValidationErrorLocItem>
      msg: string
      type: string
    source:
      openapi: tts-openapi.yml
  PostedUtteranceVoiceWithId:
    properties:
      id:
        type: string
        docs: The unique ID associated with the **Voice**.
      provider:
        type: optional<VoiceProvider>
        docs: >-
          Specifies the source provider associated with the chosen voice.


          - **`HUME_AI`**: Select voices from Hume's [Voice
          Library](https://platform.hume.ai/tts/voice-library), containing a
          variety of preset, shared voices.

          - **`CUSTOM_VOICE`**: Select from voices you've personally generated
          and saved in your account. 


          If no provider is explicitly set, the default provider is
          `CUSTOM_VOICE`. When using voices from Hume's **Voice Library**, you
          must explicitly set the provider to `HUME_AI`.


          Preset voices from Hume's **Voice Library** are accessible by all
          users. In contrast, your custom voices are private and accessible only
          via requests authenticated with your API key.
    source:
      openapi: tts-openapi.yml
  PostedUtteranceVoiceWithName:
    properties:
      name:
        type: string
        docs: The name of a **Voice**.
      provider:
        type: optional<VoiceProvider>
        docs: >-
          Specifies the source provider associated with the chosen voice.


          - **`HUME_AI`**: Select voices from Hume's [Voice
          Library](https://platform.hume.ai/tts/voice-library), containing a
          variety of preset, shared voices.

          - **`CUSTOM_VOICE`**: Select from voices you've personally generated
          and saved in your account. 


          If no provider is explicitly set, the default provider is
          `CUSTOM_VOICE`. When using voices from Hume's **Voice Library**, you
          must explicitly set the provider to `HUME_AI`.


          Preset voices from Hume's **Voice Library** are accessible by all
          users. In contrast, your custom voices are private and accessible only
          via requests authenticated with your API key.
    source:
      openapi: tts-openapi.yml
  VoiceProvider:
    enum:
      - HUME_AI
      - CUSTOM_VOICE
    source:
      openapi: tts-openapi.yml
  PostedUtteranceVoice:
    discriminated: false
    union:
      - type: PostedUtteranceVoiceWithId
      - type: PostedUtteranceVoiceWithName
    source:
      openapi: tts-openapi.yml
  FormatWav:
    properties: {}
    source:
      openapi: tts-openapi.yml
  ErrorResponse:
    properties:
      error: optional<string>
      message: optional<string>
      code: optional<string>
    source:
      openapi: tts-openapi.yml
  ReturnPagedVoices:
    docs: A paginated list Octave voices available for text-to-speech
    properties:
      page_number:
        type: optional<integer>
        docs: >-
          The page number of the returned list.


          This value corresponds to the `page_number` parameter specified in the
          request. Pagination uses zero-based indexing.
      page_size:
        type: optional<integer>
        docs: >-
          The maximum number of items returned per page.


          This value corresponds to the `page_size` parameter specified in the
          request.
      total_pages:
        type: optional<integer>
        docs: The total number of pages in the collection.
      voices_page:
        type: optional<list<ReturnVoice>>
        docs: >-
          List of voices returned for the specified `page_number` and
          `page_size`.
    source:
      openapi: tts-openapi.yml
