/**
 * This file was auto-generated by <PERSON><PERSON> from our API Definition.
 */
import * as <PERSON> from "../../../../../index";
/**
 * The Facial Emotional Expression model analyzes human facial expressions in images and videos. Results will be provided per frame for video files.
 *
 * Recommended input file types: `.png`, `.jpeg`, `.mp4`
 */
export interface Face {
    /** Number of frames per second to process. Other frames will be omitted from the response. Set to `0` to process every frame. */
    fpsPred?: number;
    /** Face detection probability threshold. Faces detected with a probability less than this threshold will be omitted from the response. */
    probThreshold?: number;
    /** Whether to return identifiers for faces across frames. If `true`, unique identifiers will be assigned to face bounding boxes to differentiate different faces. If `false`, all faces will be tagged with an `unknown` ID. */
    identifyFaces?: boolean;
    /** Minimum bounding box side length in pixels to treat as a face. Faces detected with a bounding box side length in pixels less than this threshold will be omitted from the response. */
    minFaceSize?: number;
    facs?: Hume.expressionMeasurement.batch.Unconfigurable;
    descriptions?: Hume.expressionMeasurement.batch.Unconfigurable;
    /** Whether to extract and save the detected faces in the artifacts zip created by each job. */
    saveFaces?: boolean;
}
