/**
 * This file was auto-generated by <PERSON><PERSON> from our API Definition.
 */
import * as <PERSON> from "../../../../../index";
export interface FacePrediction {
    /** Frame number */
    frame: number;
    /** Time in seconds when face detection occurred. */
    time: number;
    /** The predicted probability that a detected face was actually a face. */
    prob: number;
    box: Hume.expressionMeasurement.batch.BoundingBox;
    /** A high-dimensional embedding in emotion space. */
    emotions: Hume.expressionMeasurement.batch.EmotionScore[];
    /** FACS 2.0 features and their scores. */
    facs?: Hume.expressionMeasurement.batch.FacsScore[];
    /** Modality-specific descriptive features and their scores. */
    descriptions?: Hume.expressionMeasurement.batch.DescriptionsScore[];
}
