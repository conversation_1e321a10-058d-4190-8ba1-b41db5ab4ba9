/**
 * This file was auto-generated by <PERSON><PERSON> from our API Definition.
 */
/**
 * The granularity at which to generate predictions. The `granularity` field is ignored if transcription is not enabled or if the `window` field has been set.
 *
 * - `word`: At the word level, our model provides a separate output for each word, offering the most granular insight into emotional expression during speech.
 *
 * - `sentence`: At the sentence level of granularity, we annotate the emotional tone of each spoken sentence with our Prosody and Emotional Language models.
 *
 * - `utterance`: Utterance-level granularity is between word- and sentence-level. It takes into account natural pauses or breaks in speech, providing more rapidly updated measures of emotional expression within a flowing conversation. For text inputs, utterance-level granularity will produce results identical to sentence-level granularity.
 *
 * - `conversational_turn`: Conversational turn-level granularity provides a distinct output for each change in speaker. It captures the full sequence of words and sentences spoken uninterrupted by each person. This approach provides a higher-level view of the emotional dynamics in a multi-participant dialogue. For text inputs, specifying conversational turn-level granularity for our Emotional Language model will produce results for the entire passage.
 */
export type Granularity = "word" | "sentence" | "utterance" | "conversational_turn";
export declare const Granularity: {
    readonly Word: "word";
    readonly Sentence: "sentence";
    readonly Utterance: "utterance";
    readonly ConversationalTurn: "conversational_turn";
};
