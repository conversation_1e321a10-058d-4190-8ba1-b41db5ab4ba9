/**
 * This file was auto-generated by <PERSON><PERSON> from our API Definition.
 */
import * as <PERSON> from "../../../../../index";
export interface JobEmbeddingGeneration {
    /** The ID associated with this job. */
    jobId: string;
    userId: string;
    request: Hume.expressionMeasurement.batch.EmbeddingGenerationBaseRequest;
    state: Hume.expressionMeasurement.batch.StateEmbeddingGeneration;
}
