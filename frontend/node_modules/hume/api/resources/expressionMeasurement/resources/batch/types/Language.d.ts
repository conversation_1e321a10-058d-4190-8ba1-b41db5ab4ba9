/**
 * This file was auto-generated by <PERSON><PERSON> from our API Definition.
 */
import * as <PERSON> from "../../../../../index";
/**
 * The Emotional Language model analyzes passages of text. This also supports audio and video files by transcribing and then directly analyzing the transcribed text.
 *
 * Recommended input filetypes: `.txt`, `.mp3`, `.wav`, `.mp4`
 */
export interface Language {
    granularity?: Hume.expressionMeasurement.batch.Granularity;
    sentiment?: Hume.expressionMeasurement.batch.Unconfigurable;
    toxicity?: Hume.expressionMeasurement.batch.Unconfigurable;
    /** Whether to return identifiers for speakers over time. If `true`, unique identifiers will be assigned to spoken words to differentiate different speakers. If `false`, all speakers will be tagged with an `unknown` ID. */
    identifySpeakers?: boolean;
}
