/**
 * This file was auto-generated by <PERSON><PERSON> from our API Definition.
 */
import * as <PERSON> from "../../../../../index";
export interface ModelsPredictions {
    face?: Hume.expressionMeasurement.batch.PredictionsOptionalNullFacePrediction;
    burst?: Hume.expressionMeasurement.batch.PredictionsOptionalNullBurstPrediction;
    prosody?: Hume.expressionMeasurement.batch.PredictionsOptionalTranscriptionMetadataProsodyPrediction;
    language?: Hume.expressionMeasurement.batch.PredictionsOptionalTranscriptionMetadataLanguagePrediction;
    ner?: Hume.expressionMeasurement.batch.PredictionsOptionalTranscriptionMetadataNerPrediction;
    facemesh?: Hume.expressionMeasurement.batch.PredictionsOptionalNullFacemeshPrediction;
}
