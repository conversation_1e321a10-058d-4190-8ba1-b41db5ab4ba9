/**
 * This file was auto-generated by Fern from our API Definition.
 */
/**
 * The NER (Named-entity Recognition) model identifies real-world objects and concepts in passages of text. This also supports audio and video files by transcribing and then directly analyzing the transcribed text.
 *
 * Recommended input filetypes: `.txt`, `.mp3`, `.wav`, `.mp4`
 */
export interface Ner {
    /** Whether to return identifiers for speakers over time. If `true`, unique identifiers will be assigned to spoken words to differentiate different speakers. If `false`, all speakers will be tagged with an `unknown` ID. */
    identifySpeakers?: boolean;
}
