/**
 * This file was auto-generated by <PERSON><PERSON> from our API Definition.
 */
import * as <PERSON> from "../../../../../index";
export interface NerPrediction {
    /** The recognized topic or entity. */
    entity: string;
    position: Hume.expressionMeasurement.batch.PositionInterval;
    /** Our NER model's relative confidence in the recognized topic or entity. */
    entityConfidence: number;
    /** A measure of how often the entity is linked to by other entities. */
    support: number;
    /** A URL which provides more information about the recognized topic or entity. */
    uri: string;
    /** The specific word to which the emotion predictions are linked. */
    linkWord: string;
    time?: Hume.expressionMeasurement.batch.TimeInterval;
    /** Value between `0.0` and `1.0` that indicates our transcription model's relative confidence in this text. */
    confidence?: number;
    /** Value between `0.0` and `1.0` that indicates our transcription model's relative confidence that this text was spoken by this speaker. */
    speakerConfidence?: number;
    /** A high-dimensional embedding in emotion space. */
    emotions: Hume.expressionMeasurement.batch.EmotionScore[];
}
