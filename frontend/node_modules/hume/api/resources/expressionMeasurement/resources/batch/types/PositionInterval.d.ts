/**
 * This file was auto-generated by Fern from our API Definition.
 */
/**
 * Position of a segment of text within a larger document, measured in characters. Uses zero-based indexing. The beginning index is inclusive and the end index is exclusive.
 */
export interface PositionInterval {
    /** The index of the first character in the text segment, inclusive. */
    begin: number;
    /** The index of the last character in the text segment, exclusive. */
    end: number;
}
