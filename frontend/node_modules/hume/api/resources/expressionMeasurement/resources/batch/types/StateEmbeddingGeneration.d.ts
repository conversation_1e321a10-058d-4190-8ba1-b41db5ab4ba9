/**
 * This file was auto-generated by <PERSON><PERSON> from our API Definition.
 */
import * as <PERSON> from "../../../../../index";
export type StateEmbeddingGeneration = Hume.expressionMeasurement.batch.StateEmbeddingGeneration.Queued | Hume.expressionMeasurement.batch.StateEmbeddingGeneration.InProgress | Hume.expressionMeasurement.batch.StateEmbeddingGeneration.Completed | Hume.expressionMeasurement.batch.StateEmbeddingGeneration.Failed;
export declare namespace StateEmbeddingGeneration {
    interface Queued extends Hume.expressionMeasurement.batch.StateEmbeddingGenerationQueued {
        status: "QUEUED";
    }
    interface InProgress extends Hume.expressionMeasurement.batch.StateEmbeddingGenerationInProgress {
        status: "IN_PROGRESS";
    }
    interface Completed extends Hume.expressionMeasurement.batch.StateEmbeddingGenerationCompletedEmbeddingGeneration {
        status: "COMPLETED";
    }
    interface Failed extends Hume.expressionMeasurement.batch.StateEmbeddingGenerationFailed {
        status: "FAILED";
    }
}
