/**
 * This file was auto-generated by <PERSON><PERSON> from our API Definition.
 */
import * as <PERSON> from "../../../../../index";
export type Task = Hume.expressionMeasurement.batch.Task.Classification | Hume.expressionMeasurement.batch.Task.Regression;
export declare namespace Task {
    interface Classification extends Hume.expressionMeasurement.batch.TaskClassification {
        type: "classification";
    }
    interface Regression extends Hume.expressionMeasurement.batch.TaskRegression {
        type: "regression";
    }
}
