{"name": "hume", "version": "0.12.2", "private": false, "repository": "https://github.com/HumeAI/hume-typescript-sdk", "main": "./index.js", "types": "./index.d.ts", "scripts": {"format": "prettier . --write --ignore-unknown", "build": "tsc", "prepack": "cp -rv dist/. .", "test": "jest tests/unit", "test:browser": "jest --config jest.browser.config.mjs", "test:wire": "jest tests/wire", "wire:test": "yarn test:wire"}, "dependencies": {"url-join": "4.0.1", "node-fetch": "^2.7.0", "qs": "^6.13.1", "readable-stream": "^4.5.2", "ws": "^8.14.2", "uuid": "9.0.1", "zod": "^3.23.8"}, "devDependencies": {"@types/url-join": "4.0.1", "@types/qs": "^6.9.17", "@types/node-fetch": "^2.6.12", "@types/readable-stream": "^4.0.18", "webpack": "^5.97.1", "ts-loader": "^9.5.1", "jest": "^29.7.0", "@jest/globals": "^29.7.0", "@types/jest": "^29.5.14", "ts-jest": "^29.3.4", "jest-environment-jsdom": "^29.7.0", "msw": "^2.8.4", "@types/node": "^18.19.70", "prettier": "^3.4.2", "typescript": "~5.7.2", "@types/ws": "^8.5.9", "@types/uuid": "9.0.7"}, "browser": {"fs": false, "os": false, "path": false, "stream": false}, "packageManager": "yarn@1.22.22"}