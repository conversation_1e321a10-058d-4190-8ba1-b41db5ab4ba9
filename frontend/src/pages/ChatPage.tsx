import { useEffect, useState } from 'react'
import { useChat } from '../contexts/ChatContext'
import { useToast } from '../components/ui/Toaster'
import { Mic, Phone, PhoneOff } from 'lucide-react'

export default function ChatPage() {
  const {
    isConnected,
    isConnecting,
    connectionError,
    isChatActive,
    messages,
    isRecording,
    connect,
    disconnect: _disconnect,
    startChat,
    endChat
  } = useChat()
  
  const { addToast } = useToast()

  useEffect(() => {
    // Auto-connect when component mounts
    if (!isConnected && !isConnecting) {
      handleConnect()
    }

    return () => {
      // Cleanup handled by audio service
    }
  }, [])

  const handleConnect = async () => {
    try {
      await connect()
      addToast({
        type: 'success',
        title: 'Connected',
        message: 'Successfully connected to chat server'
      })
    } catch (error) {
      addToast({
        type: 'error',
        title: 'Connection Failed',
        message: 'Failed to connect to chat server'
      })
    }
  }

  const handleStartChat = async () => {
    try {
      // Start chat (this will handle microphone access and audio setup)
      await startChat()

      addToast({
        type: 'success',
        title: 'Chat Started',
        message: 'You can now speak with the AI'
      })
    } catch (error) {
      console.error('Failed to start chat:', error)
      addToast({
        type: 'error',
        title: 'Failed to Start Chat',
        message: 'Could not access microphone or start chat'
      })
    }
  }

  const handleEndChat = () => {
    endChat()

    addToast({
      type: 'info',
      title: 'Chat Ended',
      message: 'Chat session has been ended'
    })
  }

  const getConnectionStatus = () => {
    if (isConnecting) return { text: 'Connecting...', color: 'text-yellow-600' }
    if (isConnected) return { text: 'Connected', color: 'text-green-600' }
    if (connectionError) return { text: 'Error', color: 'text-red-600' }
    return { text: 'Disconnected', color: 'text-gray-600' }
  }

  const status = getConnectionStatus()

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 p-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Voice Chat</h1>
            <p className="text-sm text-gray-500">
              Have a conversation with our empathic AI
            </p>
          </div>
          
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`} />
              <span className={`text-sm ${status.color}`}>{status.text}</span>
            </div>
            
            {!isConnected && !isConnecting && (
              <button
                onClick={handleConnect}
                className="btn-primary text-sm"
              >
                Reconnect
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Chat Area */}
      <div className="flex-1 flex flex-col">
        {/* Messages */}
        <div className="flex-1 overflow-y-auto p-4 space-y-4">
          {messages.length === 0 ? (
            <div className="text-center py-12">
              <div className="text-gray-400 mb-4">
                <Mic className="w-16 h-16 mx-auto" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Ready to Chat
              </h3>
              <p className="text-gray-500 mb-6">
                Start a conversation to see messages appear here
              </p>
              {!isChatActive && isConnected && (
                <button
                  onClick={handleStartChat}
                  className="btn-primary"
                >
                  Start Conversation
                </button>
              )}
            </div>
          ) : (
            messages.map((message, index) => (
              <div
                key={index}
                className={`chat-message ${message.role}`}
              >
                <div className="flex items-start space-x-3">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                    message.role === 'user' ? 'bg-primary-600' : 'bg-gray-600'
                  }`}>
                    <span className="text-white text-sm font-medium">
                      {message.role === 'user' ? 'U' : 'AI'}
                    </span>
                  </div>
                  
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-1">
                      <span className="font-medium text-gray-900">
                        {message.role === 'user' ? 'You' : 'Assistant'}
                      </span>
                      <span className="text-xs text-gray-500">
                        {message.timestamp.toLocaleTimeString()}
                      </span>
                    </div>
                    
                    <p className="text-gray-800">{message.content}</p>
                    
                    {/* Emotions */}
                    {message.emotions && Object.keys(message.emotions).length > 0 && (
                      <div className="mt-2 flex flex-wrap gap-1">
                        {Object.entries(message.emotions)
                          .sort(([,a], [,b]) => b - a)
                          .slice(0, 3)
                          .map(([emotion, score]) => (
                            <span
                              key={emotion}
                              className={`emotion-badge ${
                                score > 0.7 ? 'emotion-high' :
                                score > 0.4 ? 'emotion-medium' : 'emotion-low'
                              }`}
                            >
                              {emotion}: {(score * 100).toFixed(0)}%
                            </span>
                          ))}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))
          )}
        </div>

        {/* Controls */}
        <div className="bg-white border-t border-gray-200 p-4">
          <div className="flex items-center justify-center space-x-4">
            {!isChatActive ? (
              <button
                onClick={handleStartChat}
                disabled={!isConnected}
                className={`flex items-center space-x-2 px-6 py-3 rounded-full ${
                  isConnected 
                    ? 'bg-green-600 hover:bg-green-700 text-white' 
                    : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                }`}
              >
                <Phone className="w-5 h-5" />
                <span>Start Chat</span>
              </button>
            ) : (
              <div className="flex items-center space-x-4">
                <div className={`flex items-center space-x-2 px-4 py-2 rounded-full ${
                  isRecording ? 'bg-red-100 text-red-800' : 'bg-gray-100 text-gray-600'
                }`}>
                  {isRecording ? (
                    <>
                      <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse" />
                      <span className="text-sm">Recording...</span>
                    </>
                  ) : (
                    <>
                      <Mic className="w-4 h-4" />
                      <span className="text-sm">Ready to listen</span>
                    </>
                  )}
                </div>
                
                <button
                  onClick={handleEndChat}
                  className="flex items-center space-x-2 px-6 py-3 rounded-full bg-red-600 hover:bg-red-700 text-white"
                >
                  <PhoneOff className="w-5 h-5" />
                  <span>End Chat</span>
                </button>
              </div>
            )}
          </div>
          
          {connectionError && (
            <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
              <p className="text-sm text-red-800">{connectionError}</p>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
