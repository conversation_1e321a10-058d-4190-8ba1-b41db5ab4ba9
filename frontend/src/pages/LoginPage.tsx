import { useEffect, useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { useAuth } from '../contexts/AuthContext'
import { LoadingSpinner } from '../components/ui/LoadingSpinner'
import { useToast } from '../components/ui/Toaster'

declare global {
  interface Window {
    google: any
  }
}

export default function LoginPage() {
  const navigate = useNavigate()
  const { login } = useAuth()
  const { addToast } = useToast()
  const [isLoading, setIsLoading] = useState(false)

  useEffect(() => {
    // Initialize Google Sign-In
    if (window.google) {
      initializeGoogleSignIn()
    } else {
      // Wait for Google script to load
      const checkGoogle = setInterval(() => {
        if (window.google) {
          clearInterval(checkGoogle)
          initializeGoogleSignIn()
        }
      }, 100)
    }
  }, [])

  const initializeGoogleSignIn = () => {
    window.google.accounts.id.initialize({
      client_id: import.meta.env.VITE_GOOGLE_CLIENT_ID,
      callback: handleGoogleSignIn,
      auto_select: false,
      cancel_on_tap_outside: true
    })

    window.google.accounts.id.renderButton(
      document.getElementById('google-signin-button'),
      {
        theme: 'outline',
        size: 'large',
        width: 300,
        text: 'signin_with',
        shape: 'rectangular'
      }
    )
  }

  const handleGoogleSignIn = async (response: any) => {
    try {
      setIsLoading(true)
      await login(response.credential)
      
      addToast({
        type: 'success',
        title: 'Welcome!',
        message: 'Successfully signed in with Google'
      })
      
      navigate('/dashboard')
    } catch (error) {
      console.error('Login failed:', error)
      addToast({
        type: 'error',
        title: 'Login Failed',
        message: error instanceof Error ? error.message : 'Failed to sign in with Google'
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-50 to-secondary-100 flex items-center justify-center p-4">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <div className="mx-auto h-16 w-16 bg-primary-600 rounded-full flex items-center justify-center mb-6">
            <svg
              className="h-8 w-8 text-white"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z"
              />
            </svg>
          </div>
          <h2 className="text-3xl font-bold text-gray-900">Welcome to ORA</h2>
          <p className="mt-2 text-sm text-gray-600">
            Experience empathic conversations powered by Hume AI
          </p>
        </div>

        <div className="bg-white rounded-lg shadow-lg p-8 space-y-6">
          <div className="text-center">
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Sign in to get started
            </h3>
            <p className="text-sm text-gray-500 mb-6">
              Connect with our AI that understands emotions and responds with empathy
            </p>
          </div>

          <div className="space-y-4">
            <div className="flex justify-center">
              {isLoading ? (
                <div className="flex items-center justify-center w-[300px] h-[44px] border border-gray-300 rounded">
                  <LoadingSpinner size="sm" />
                  <span className="ml-2 text-sm text-gray-600">Signing in...</span>
                </div>
              ) : (
                <div id="google-signin-button"></div>
              )}
            </div>

            <div className="text-center">
              <p className="text-xs text-gray-500">
                By signing in, you agree to our{' '}
                <a href="#" className="text-primary-600 hover:text-primary-500">
                  Terms of Service
                </a>{' '}
                and{' '}
                <a href="#" className="text-primary-600 hover:text-primary-500">
                  Privacy Policy
                </a>
              </p>
            </div>
          </div>

          <div className="border-t border-gray-200 pt-6">
            <div className="text-center space-y-2">
              <h4 className="text-sm font-medium text-gray-900">What you can do:</h4>
              <ul className="text-xs text-gray-600 space-y-1">
                <li>• Have natural voice conversations with AI</li>
                <li>• Get real-time emotion analysis</li>
                <li>• View conversation transcripts and insights</li>
                <li>• Customize your profile and preferences</li>
              </ul>
            </div>
          </div>
        </div>

        <div className="text-center">
          <p className="text-xs text-gray-500">
            Powered by{' '}
            <a
              href="https://hume.ai"
              target="_blank"
              rel="noopener noreferrer"
              className="text-primary-600 hover:text-primary-500"
            >
              Hume AI
            </a>
          </p>
        </div>
      </div>
    </div>
  )
}
