import { apiService } from './api'
import type { WebSocketMessage } from '@shared/types'

export type WebSocketEventHandler = (message: WebSocketMessage) => void

export class WebSocketService {
  private ws: WebSocket | null = null
  private reconnectAttempts = 0
  private maxReconnectAttempts = 5
  private reconnectDelay = 1000
  private eventHandlers: Map<string, WebSocketEventHandler[]> = new Map()
  private isConnecting = false
  private shouldReconnect = true

  async connect(): Promise<void> {
    if (this.ws?.readyState === WebSocket.OPEN || this.isConnecting) {
      return
    }

    this.isConnecting = true

    try {
      const token = apiService.getAccessToken()
      if (!token) {
        throw new Error('No access token available')
      }

      const wsUrl = apiService.getWebSocketUrl(token)
      console.log('🔌 Connecting to WebSocket:', wsUrl)

      this.ws = new WebSocket(wsUrl)
      
      this.ws.onopen = this.handleOpen.bind(this)
      this.ws.onmessage = this.handleMessage.bind(this)
      this.ws.onclose = this.handleClose.bind(this)
      this.ws.onerror = this.handleError.bind(this)

      // Wait for connection to open
      await new Promise<void>((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error('WebSocket connection timeout'))
        }, 10000)

        this.ws!.onopen = (event) => {
          clearTimeout(timeout)
          this.handleOpen(event)
          resolve()
        }

        this.ws!.onerror = (_event) => {
          clearTimeout(timeout)
          reject(new Error('WebSocket connection failed'))
        }
      })

    } catch (error) {
      console.error('WebSocket connection error:', error)
      this.isConnecting = false
      throw error
    }
  }

  disconnect(): void {
    this.shouldReconnect = false
    if (this.ws) {
      this.ws.close(1000, 'User disconnected')
      this.ws = null
    }
  }

  send(message: WebSocketMessage): void {
    if (this.ws?.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(message))
    } else {
      console.warn('WebSocket not connected, message not sent:', message)
    }
  }

  on(eventType: string, handler: WebSocketEventHandler): void {
    if (!this.eventHandlers.has(eventType)) {
      this.eventHandlers.set(eventType, [])
    }
    this.eventHandlers.get(eventType)!.push(handler)
  }

  off(eventType: string, handler: WebSocketEventHandler): void {
    const handlers = this.eventHandlers.get(eventType)
    if (handlers) {
      const index = handlers.indexOf(handler)
      if (index > -1) {
        handlers.splice(index, 1)
      }
    }
  }

  private handleOpen(_event: Event): void {
    console.log('✅ WebSocket connected')
    this.isConnecting = false
    this.reconnectAttempts = 0
    this.emit('connection_established', {
      type: 'connection_established',
      data: { connected: true },
      timestamp: new Date()
    })
  }

  private handleMessage(event: MessageEvent): void {
    try {
      const message: WebSocketMessage = JSON.parse(event.data)
      console.log('📨 WebSocket message received:', message.type)
      this.emit(message.type, message)
      this.emit('*', message) // Emit to wildcard listeners
    } catch (error) {
      console.error('Failed to parse WebSocket message:', error)
    }
  }

  private handleClose(event: CloseEvent): void {
    console.log('🔌 WebSocket disconnected:', event.code, event.reason)
    this.isConnecting = false
    this.ws = null

    this.emit('connection_lost', {
      type: 'connection_lost',
      data: { code: event.code, reason: event.reason },
      timestamp: new Date()
    })

    // Attempt to reconnect if it wasn't a clean close
    if (this.shouldReconnect && event.code !== 1000) {
      this.attemptReconnect()
    }
  }

  private handleError(event: Event): void {
    console.error('❌ WebSocket error:', event)
    this.emit('connection_error', {
      type: 'connection_error',
      data: { error: 'WebSocket error occurred' },
      timestamp: new Date()
    })
  }

  private async attemptReconnect(): Promise<void> {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('Max reconnection attempts reached')
      this.emit('connection_failed', {
        type: 'connection_failed',
        data: { reason: 'Max reconnection attempts reached' },
        timestamp: new Date()
      })
      return
    }

    this.reconnectAttempts++
    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1)
    
    console.log(`🔄 Attempting to reconnect in ${delay}ms (attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts})`)
    
    setTimeout(async () => {
      try {
        await this.connect()
      } catch (error) {
        console.error('Reconnection failed:', error)
        this.attemptReconnect()
      }
    }, delay)
  }

  private emit(eventType: string, message: WebSocketMessage): void {
    const handlers = this.eventHandlers.get(eventType) || []
    handlers.forEach(handler => {
      try {
        handler(message)
      } catch (error) {
        console.error('Error in WebSocket event handler:', error)
      }
    })
  }

  // Chat-specific methods
  startChat(resumedChatGroupId?: string): void {
    this.send({
      type: 'start_chat',
      data: { resumedChatGroupId },
      timestamp: new Date()
    })
  }

  endChat(reason = 'user_ended'): void {
    this.send({
      type: 'end_chat',
      data: { reason },
      timestamp: new Date()
    })
  }

  sendAudioInput(audioData: string): void {
    console.log('📤 Sending audio input, data length:', audioData.length)
    this.send({
      type: 'audio_input',
      data: { data: audioData },
      timestamp: new Date()
    })
  }

  sendTextInput(text: string): void {
    this.send({
      type: 'text_input',
      data: { text },
      timestamp: new Date()
    })
  }

  ping(): void {
    this.send({
      type: 'ping',
      data: {},
      timestamp: new Date()
    })
  }

  // Connection status
  get isConnected(): boolean {
    return this.ws?.readyState === WebSocket.OPEN
  }

  get connectionState(): string {
    if (!this.ws) return 'disconnected'
    
    switch (this.ws.readyState) {
      case WebSocket.CONNECTING: return 'connecting'
      case WebSocket.OPEN: return 'connected'
      case WebSocket.CLOSING: return 'closing'
      case WebSocket.CLOSED: return 'disconnected'
      default: return 'unknown'
    }
  }
}

// Create singleton instance
export const webSocketService = new WebSocketService()
export default webSocketService
